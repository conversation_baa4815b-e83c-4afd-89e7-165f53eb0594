# Instagram Reels Uploader with Gemini Captions

This Python script automates the process of uploading videos to Instagram as <PERSON><PERSON>. It intelligently generates captions using Google's Gemini API, tracks uploaded videos to prevent duplicates, and provides clear, readable logs.

## 🚀 Features

-   **Automated Batch Uploads**: Uploads all videos from a specified folder in sequential order.
-   **Dynamic Caption Generation**: Uses the Google Gemini API to generate engaging captions, including the original Sanskrit verse, transliteration, and translation for each video.
-   **Duplicate Prevention**: Keeps a log of uploaded videos to ensure you don't upload the same video twice.
-   **Modular and Clean Code**: The project is broken down into logical modules, making it easy to understand and maintain.
-   **Error Handling**: The script is designed to handle common errors and provides clear feedback.

## 📋 Prerequisites

1.  **Instagram Business/Creator Account**: Your Instagram account must be a Business or Creator account and linked to a Facebook Page.
2.  **Facebook Developer App**: You'll need a Facebook Developer App with the necessary permissions.
3.  **Page Access Token**: A valid Page Access Token with the following permissions:
    *   `instagram_content_publish`
    *   `pages_read_engagement`
4.  **Instagram Business Account ID**: Your Instagram Business Account ID.
5.  **Google API Key**: A Google API key with access to the Gemini API.

## 🛠 Setup

1.  **Install Dependencies**:
    ```bash
    pip install -r requirements.txt
    ```
2.  **Configure Your Settings**:

    Open the `config.ini` file and update the following fields:

    *   `PAGE_TOKEN`: Your Facebook Page Access Token.
    *   `IG_BUSINESS_ID`: Your Instagram Business Account ID.
    *   `VIDEO_FOLDER`: The full path to the folder containing your video files.
    *   `GOOGLE_API_KEY`: Your Google API key.

## 🎯 Usage

Once you've completed the setup, you can run the script with a single command:

```bash
python3 main.py
```

The script will then:
1.  Read all the video files from the folder you specified in `config.ini`.
2.  Sort them in the correct order based on chapter and verse numbers in the filenames.
3.  Check the `uploaded_videos.log` file to see which videos have already been uploaded.
4.  For each new video, it will:
    *   Generate a caption using the Gemini API.
    *   Upload the video to Instagram as a Reel.
    *   Log the successful upload in `uploaded_videos.log`.

## 📁 File Structure

```
.
├── config.ini          # Configuration file for your credentials and settings
├── config.py           # Module to load and manage the configuration
├── gemini.py           # Module for interacting with the Google Gemini API
├── instagram.py        # Module for handling Instagram uploads
├── logger.py           # Module for logging and tracking uploaded videos
├── main.py             # The main script to run the application
├── README.md           # This file
├── requirements.txt    # Python dependencies
└── uploaded_videos.log # Log file to track uploaded videos
```

## 🤝 Support

If you encounter any issues, please check the following:
*   Ensure your `PAGE_TOKEN` and `GOOGLE_API_KEY` are valid and have not expired.
*   Verify that the `VIDEO_FOLDER` path in `config.ini` is correct.
*   Check the console output for any error messages from the script.
