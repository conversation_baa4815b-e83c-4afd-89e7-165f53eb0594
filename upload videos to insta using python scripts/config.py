import os
import sys
import configparser

def load_config():
    """Load configuration from config.ini file"""
    config = configparser.ConfigParser()
    config_file = os.path.join(os.path.dirname(__file__), 'config.ini')
    
    if not os.path.exists(config_file):
        print("❌ Error: config.ini file not found!")
        print(f"Expected location: {config_file}")
        sys.exit(1)
    
    try:
        config.read(config_file)
        
        # Parse supported formats
        formats_str = config.get('file_formats', 'SUPPORTED_FORMATS')
        supported_formats = [fmt.strip() for fmt in formats_str.split(',')]
        
        return {
            'PAGE_TOKEN': config.get('credentials', 'PAGE_TOKEN'),
            'IG_BUSINESS_ID': config.get('credentials', 'IG_BUSINESS_ID'),
            'VIDEO_FOLDER': config.get('video_settings', 'VIDEO_FOLDER'),
            'LOG_FILE': config.get('video_settings', 'LOG_FILE'),
            'GOOGLE_API_KEY': config.get('google', 'GOOGLE_API_KEY'),
            'API_VERSION': config.get('api_settings', 'API_VERSION'),
            'MAX_FILE_SIZE_MB': config.getint('api_settings', 'MAX_FILE_SIZE_MB'),
            'SUPPORTED_FORMATS': supported_formats,
            'RETRY_ATTEMPTS': config.getint('api_settings', 'RETRY_ATTEMPTS'),
            'RETRY_DELAY': config.getint('api_settings', 'RETRY_DELAY')
        }
    except (configparser.Error, ValueError) as e:
        print(f"❌ Error reading config.ini: {e}")
        print("Please check your config.ini file format.")
        sys.exit(1)

CONFIG = load_config()
