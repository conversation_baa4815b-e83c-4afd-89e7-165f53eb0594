import os
import re
import sys
import time
from pathlib import Path
from tqdm import tqdm
from config import CONFIG
from logger import log_info, log_error, log_warning, get_uploaded_videos, log_uploaded_video
from gemini import configure_gemini, generate_caption_and_tags
from instagram import InstagramReelsUploader

def get_video_files(video_folder):
    """Get a sorted list of video files from the specified folder."""
    if not os.path.exists(video_folder):
        log_error(f"Video folder not found: {video_folder}")
        return []
    
    video_files = []
    for ext in CONFIG['SUPPORTED_FORMATS']:
        video_files.extend(Path(video_folder).glob(f"*{ext}"))
    
    # Filter out files with suffixes like -telugu
    video_files = [f for f in video_files if '-' not in f.stem]

    # Sort files based on chapter and verse numbers for proper serial order
    def get_chapter_verse(filename):
        """Extract chapter and verse numbers for proper sorting."""
        match = re.search(r'Chapter_(\d+)_Verse_(\d+)', filename.stem)
        if match:
            chapter, verse = map(int, match.groups())
            return (chapter, verse)
        return (999, 999)  # Put non-matching files at the end
    
    video_files.sort(key=get_chapter_verse)
    return video_files

def main():
    """Main function to run the upload process."""
    log_info("Starting the Instagram Reels Uploader")

    # Configure Gemini
    if not configure_gemini(CONFIG['GOOGLE_API_KEY']):
        sys.exit(1)

    # Initialize Instagram Uploader
    uploader = InstagramReelsUploader(
        page_token=CONFIG['PAGE_TOKEN'],
        ig_business_id=CONFIG['IG_BUSINESS_ID'],
        api_version=CONFIG['API_VERSION']
    )

    # Get video files
    video_files = get_video_files(CONFIG['VIDEO_FOLDER'])
    if not video_files:
        log_warning("No video files found to upload.")
        sys.exit(0)

    # Get already uploaded videos
    uploaded_videos = get_uploaded_videos(CONFIG['LOG_FILE'])
    log_info(f"Found {len(uploaded_videos)} previously uploaded videos.")

    # Filter out already uploaded videos
    videos_to_upload = [vf for vf in video_files if vf.name not in uploaded_videos]
    if not videos_to_upload:
        log_info("All videos have already been uploaded.")
        sys.exit(0)

    log_info(f"Found {len(videos_to_upload)} new videos to upload.")

    # Process and upload videos in serial order with retry mechanism
    for video_file in tqdm(videos_to_upload, desc="Uploading Reels"):
        log_info(f"Processing video: {video_file.name}")
        
        # Extract chapter and verse from filename
        match = re.search(r'Chapter_(\d+)_Verse_(\d+)', video_file.name)
        if not match:
            log_warning(f"Could not extract chapter/verse from '{video_file.name}'. Skipping.")
            continue
        
        chapter, verse = map(int, match.groups())  # Convert to integers
        
        # Generate caption
        caption = generate_caption_and_tags(chapter, verse)
        if not caption:
            log_error(f"Failed to generate caption for {video_file.name}. Retrying caption generation...")
            # Retry caption generation
            for caption_retry in range(3):
                time.sleep(2)  # Brief wait before retry
                caption = generate_caption_and_tags(chapter, verse)
                if caption:
                    log_info(f"Caption generated successfully on retry {caption_retry + 1}")
                    break
            
            if not caption:
                log_error(f"Failed to generate caption for {video_file.name} after retries. Stopping the script.")
                sys.exit(1)
        
        # Upload reel with retry mechanism
        upload_success = upload_video_with_retry(uploader, str(video_file), caption, video_file.name)
        
        if upload_success:
            log_uploaded_video(CONFIG['LOG_FILE'], video_file.name)
            log_info(f"✅ Successfully uploaded {video_file.name}")
            # Wait between uploads to avoid rate limiting
            time.sleep(CONFIG['RETRY_DELAY'])
        else:
            log_error(f"❌ CRITICAL: Failed to upload {video_file.name} after all retries. Stopping script to maintain order.")
            sys.exit(1)

    log_info("All videos processed successfully! Script finished.")

def upload_video_with_retry(uploader, video_path: str, caption: str, filename: str, max_retries: int = 5) -> bool:
    """Upload video with exponential backoff retry mechanism."""
    
    for attempt in range(max_retries):
        try:
            log_info(f"📤 Upload attempt {attempt + 1}/{max_retries} for {filename}")
            
            # Attempt upload
            media_id = uploader.upload_reel(video_path, caption)
            
            if media_id:
                log_info(f"✅ Upload successful! Media ID: {media_id}")
                return True
            else:
                log_warning(f"⚠️ Upload attempt {attempt + 1} failed for {filename}")
                
        except Exception as e:
            log_error(f"❌ Upload attempt {attempt + 1} failed with exception: {e}")
        
        # If not the last attempt, wait with exponential backoff
        if attempt < max_retries - 1:
            wait_time = (2 ** attempt) * CONFIG['RETRY_DELAY']  # Exponential backoff
            log_info(f"⏳ Waiting {wait_time} seconds before retry {attempt + 2}...")
            time.sleep(wait_time)
    
    log_error(f"❌ All {max_retries} upload attempts failed for {filename}")
    return False

if __name__ == "__main__":
    main()
