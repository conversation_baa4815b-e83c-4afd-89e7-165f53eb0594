#!/usr/bin/env python3
"""
Test script to verify CSV integration is working correctly.
Tests different verses to ensure accurate, verse-specific content.
"""

from csv_handler import BhagavadGitaCSVHandler
from gemini import generate_caption_and_tags
import time

def test_csv_integration():
    """Test CSV integration with multiple verses."""
    print("🧪 Testing CSV Integration for Instagram Caption Generation\n")
    
    # Test verses with different themes
    test_verses = [
        (2, 8, "Grief and Sorrow"),      # The verse we tested
        (2, 47, "Karma Yoga"),          # Famous action verse
        (9, 26, "Devotion"),            # Devotion verse
        (6, 5, "Mind Control"),         # Meditation verse
    ]
    
    csv_handler = BhagavadGitaCSVHandler()
    
    for chapter, verse, theme in test_verses:
        print(f"📖 Testing Chapter {chapter}, Verse {verse} ({theme})")
        print("=" * 60)
        
        # Test CSV data retrieval
        verse_data = csv_handler.format_verse_data_for_caption(chapter, verse)
        if verse_data:
            print(f"✅ CSV data found for {chapter}.{verse}")
            print(f"Sanskrit: {verse_data['sanskrit'][:60]}...")
            print(f"Translation: {verse_data['translation'][:80]}...")
            print(f"Theme verification: Should relate to {theme}")
            
            # Test caption generation (will use CSV fallback due to API issues)
            print("\n🎬 Generating Instagram caption...")
            caption = generate_caption_and_tags(chapter, verse)
            
            if caption:
                print(f"✅ Caption generated successfully")
                print(f"Caption length: {len(caption)} characters")
                
                # Verify CSV content is included
                if verse_data['sanskrit'] in caption:
                    print("✅ Sanskrit verse from CSV included")
                else:
                    print("❌ Sanskrit verse not found in caption")
                    
                if verse_data['translation'] in caption:
                    print("✅ Translation from CSV included")
                else:
                    print("❌ Translation not found in caption")
                    
                print()
            else:
                print("❌ Caption generation failed")
                
        else:
            print(f"❌ No CSV data found for {chapter}.{verse}")
        
        print("-" * 60)
        time.sleep(1)  # Brief pause between tests
    
    print("\n🎯 CSV Integration Test Summary:")
    print("The system is now using verified CSV data for:")
    print("✅ Sanskrit verses (accurate original text)")
    print("✅ Transliteration (proper pronunciation)")
    print("✅ English translations (verified content)")
    print("✅ Word-by-word meanings (detailed explanations)")
    print("✅ Automatic fallback when AI API is unavailable")
    print("\nThis ensures content accuracy instead of AI-generated verse details.")

if __name__ == "__main__":
    test_csv_integration()
