/**
 * Enhanced Video Generation Service
 * Supports 9:16 vertical video creation with ASS subtitle integration
 * Optimized for mobile viewing and karaoke-style highlighting
 * Now includes background music support with fade effects and volume control
 */

const express = require('express');
const path = require('path');
const fs = require('fs').promises;
const { exec, spawn } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

// Global process tracking for cleanup
const runningProcesses = new Map();
const tempFiles = new Set();
const app = express();
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Add CORS headers for n8n compatibility
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
    if (req.method === 'OPTIONS') {
        res.sendStatus(200);
    } else {
        next();
    }
});

// Graceful shutdown handlers
async function gracefulShutdown(signal) {
    console.log(`\n🛑 Received ${signal}. Starting graceful shutdown...`);
    
    // Stop accepting new requests
    console.log('📥 Stopping new request acceptance...');
    
    // Clean up running processes
    if (runningProcesses.size > 0) {
        console.log(`🔄 Terminating ${runningProcesses.size} running FFmpeg processes...`);
        for (const [requestId, process] of runningProcesses) {
            try {
                console.log(`⏹️ Killing process for request: ${requestId}`);
                process.kill('SIGKILL');
            } catch (error) {
                console.warn(`⚠️ Error killing process ${requestId}:`, error.message);
            }
        }
        runningProcesses.clear();
    }
    
    // Clean up temporary files
    if (tempFiles.size > 0) {
        console.log(`🧹 Cleaning up ${tempFiles.size} temporary files...`);
        await cleanupTempFiles();
    }
    
    console.log('✅ Graceful shutdown completed');
    process.exit(0);
}

// Register shutdown handlers
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
    console.error('💥 Uncaught Exception:', error);
    gracefulShutdown('UNCAUGHT_EXCEPTION');
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('💥 Unhandled Rejection at:', promise, 'reason:', reason);
    gracefulShutdown('UNHANDLED_REJECTION');
});

// Periodic cleanup of old temp files (every 30 minutes)
setInterval(async () => {
    console.log('🧹 Running periodic temp file cleanup...');
    await cleanupTempFiles();
}, 30 * 60 * 1000);

const PORT = process.env.PORT || 3002;

// Processing configuration with timeouts and limits
const PROCESSING_CONFIG = {
    timeout: 20 * 60 * 1000, // 20 minutes timeout
    maxFileSize: 500 * 1024 * 1024, // 500MB max file size
    maxConcurrentJobs: 3,
    allowedAudioFormats: ['.mp3', '.wav', '.flac', '.m4a', '.aac'],
    allowedImageFormats: ['.jpg', '.jpeg', '.png', '.bmp', '.webp'],
    allowedVideoFormats: ['.mp4', '.mov', '.avi', '.mkv']
};

// Background music configuration
const BACKGROUND_MUSIC_CONFIG = {
    filePath: path.join(__dirname, 'shared-data', 'media', 'bgm_for_bhagavad_gita.mp3'),
    volume: 1.5,  // 150% volume
    primaryAudioVolume: 1.5,  // 150% volume for speech
    fadeInDuration: 0,   // No fade-in
    fadeOutDuration: 3   // 3 seconds fade-out
};

// Enhanced configuration for 9:16 vertical videos (UPDATED FOR YOUTUBE QUALITY)
const VIDEO_CONFIG = {
    // Optimized 432x768 (9:16) for YouTube Shorts with maximum quality
    MOBILE_PORTRAIT: {
        width: 432,    // Keep original 432x768 as requested
        height: 768,   // Keep original 768 height for 9:16 aspect ratio
        name: 'YouTube Shorts (9:16) - 432x768 Max Quality'
    },
    // Alternative high quality option
    HD_PORTRAIT: {
        width: 1080,
        height: 1920,
        name: 'HD Portrait (9:16) - 1080x1920'
    },
    ENCODING: {
        videoCodec: 'libx264',
        audioCodec: 'aac',
        preset: 'faster',     // Changed from 'medium' to 'faster' for better performance
        profile: 'main',      // Changed from 'high' to 'main' for better compatibility
        level: '3.1',         // Reduced from '4.1' to '3.1' for mobile compatibility
        pixelFormat: 'yuv420p',
        // Optimized quality settings for faster processing and better compatibility
        tune: 'zerolatency',  // Optimize for fast encoding
        bitrate: '1200k',     // Reduced bitrate for faster processing
        audioBitrate: '96k'   // Reduced audio bitrate for faster processing
    }
};

/**
 * Utility function to check if file exists
 */
async function fileExists(filePath) {
    try {
        await fs.access(filePath);
        return true;
    } catch {
        return false;
    }
}

/**
 * Validate and sanitize filename to prevent path traversal and injection
 */
function validateAndSanitizeFilename(filename, allowedExtensions, fileType = 'file') {
    if (!filename || typeof filename !== 'string') {
        throw new Error(`${fileType} filename is required and must be a string`);
    }

    // Remove any path separators and normalize
    const sanitized = path.basename(filename.trim());
    
    // Check for empty filename after sanitization
    if (!sanitized || sanitized === '.' || sanitized === '..') {
        throw new Error(`Invalid ${fileType} filename`);
    }

    // Check for path traversal attempts
    if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
        throw new Error(`${fileType} filename contains invalid path characters`);
    }

    // Check file extension
    const ext = path.extname(sanitized).toLowerCase();
    if (!allowedExtensions.includes(ext)) {
        throw new Error(`${fileType} format not supported. Allowed formats: ${allowedExtensions.join(', ')}`);
    }

    // Check filename length
    if (sanitized.length > 255) {
        throw new Error(`${fileType} filename too long (max 255 characters)`);
    }

    // Check for special characters that might cause issues
    if (!/^[a-zA-Z0-9._-]+$/.test(sanitized)) {
        throw new Error(`${fileType} filename contains invalid characters. Only alphanumeric, dots, hyphens, and underscores allowed`);
    }

    return sanitized;
}

/**
 * Validate file size and existence
 */
async function validateFile(filePath, maxSize, fileType = 'file') {
    if (!await fileExists(filePath)) {
        throw new Error(`${fileType} not found: ${path.basename(filePath)}`);
    }

    const stats = await fs.stat(filePath);
    if (stats.size === 0) {
        throw new Error(`${fileType} is empty: ${path.basename(filePath)}`);
    }

    if (stats.size > maxSize) {
        const maxSizeMB = Math.round(maxSize / (1024 * 1024));
        const fileSizeMB = Math.round(stats.size / (1024 * 1024));
        throw new Error(`${fileType} too large (${fileSizeMB}MB). Maximum allowed: ${maxSizeMB}MB`);
    }

    return stats;
}

/**
 * Add temporary file to cleanup list
 */
function addTempFile(filePath) {
    tempFiles.add(filePath);
    return filePath;
}

/**
 * Clean up temporary files
 */
async function cleanupTempFiles() {
    const cleanupPromises = Array.from(tempFiles).map(async (file) => {
        try {
            await fs.unlink(file);
            tempFiles.delete(file);
            console.log(`🧹 Cleaned up temp file: ${path.basename(file)}`);
        } catch (error) {
            if (error.code !== 'ENOENT') { // File not found is OK
                console.warn(`⚠️ Could not delete temp file: ${path.basename(file)} - ${error.message}`);
            }
            tempFiles.delete(file); // Remove from set anyway
        }
    });

    await Promise.allSettled(cleanupPromises);
}

/**
 * Execute FFmpeg command with timeout and process tracking
 */
async function executeFFmpegWithTimeout(command, requestId, timeoutMs = PROCESSING_CONFIG.timeout) {
    return new Promise((resolve, reject) => {
        console.log(`🎬 [${requestId}] Starting FFmpeg with ${timeoutMs/1000}s timeout`);
        
        const childProcess = exec(command, { 
            maxBuffer: 10 * 1024 * 1024, // 10MB buffer for output
            encoding: 'utf8'
        });
        
        // Track the process
        runningProcesses.set(requestId, childProcess);

        let isResolved = false;

        // Set up timeout
        const timeoutHandle = setTimeout(() => {
            if (!isResolved) {
                isResolved = true;
                console.log(`⏰ [${requestId}] FFmpeg process timed out after ${timeoutMs/1000}s`);
                
                // Kill the process
                try {
                    childProcess.kill('SIGKILL');
                } catch (error) {
                    console.warn(`⚠️ [${requestId}] Error killing process:`, error.message);
                }
                
                runningProcesses.delete(requestId);
                reject(new Error(`Video processing timed out after ${timeoutMs/60000} minutes. File may be too large or corrupted.`));
            }
        }, timeoutMs);

        // Handle process completion
        childProcess.on('close', (code, signal) => {
            if (!isResolved) {
                isResolved = true;
                clearTimeout(timeoutHandle);
                runningProcesses.delete(requestId);

                if (code === 0) {
                    console.log(`✅ [${requestId}] FFmpeg completed successfully`);
                    resolve({ stdout: stdout, stderr: stderr });
                } else if (signal === 'SIGKILL') {
                    reject(new Error('Video processing was terminated due to timeout or system limits'));
                } else {
                    reject(new Error(`FFmpeg process failed with exit code ${code}`));
                }
            }
        });

        // Capture output for error analysis
        let stdout = '';
        let stderr = '';

        childProcess.stdout?.on('data', (data) => {
            stdout += data;
        });

        childProcess.stderr?.on('data', (data) => {
            stderr += data;
        });

        // Handle process errors
        childProcess.on('error', (error) => {
            if (!isResolved) {
                isResolved = true;
                clearTimeout(timeoutHandle);
                runningProcesses.delete(requestId);
                
                // Enhanced error analysis
                if (error.code === 'ENOENT') {
                    reject(new Error('FFmpeg not found. Please ensure FFmpeg is installed and accessible.'));
                } else if (error.code === 'EMFILE' || error.code === 'ENFILE') {
                    reject(new Error('Too many open files. Server is overloaded, please try again later.'));
                } else {
                    reject(new Error(`FFmpeg execution error: ${error.message}`));
                }
            }
        });

        // Enhanced stderr analysis on exit
        childProcess.on('exit', (code) => {
            if (code !== 0 && stderr) {
                // Analyze stderr for common issues
                if (stderr.includes('Invalid data found when processing input')) {
                    reject(new Error('Input file is corrupted or in an unsupported format'));
                } else if (stderr.includes('No space left on device')) {
                    reject(new Error('Server storage is full. Please try again later.'));
                } else if (stderr.includes('Permission denied')) {
                    reject(new Error('File permission error. Please check file accessibility.'));
                } else if (stderr.includes('codec not supported')) {
                    reject(new Error('Audio or video codec not supported by this FFmpeg installation'));
                } else if (stderr.includes('Cannot allocate memory')) {
                    reject(new Error('Insufficient memory to process this file. Please try with a smaller file.'));
                } else if (stderr.includes('Protocol not found')) {
                    reject(new Error('Network protocol error. Please check file source.'));
                }
            }
        });
    });
}

/**
 * Health check endpoint
 */
app.get('/health', async (req, res) => {
    const backgroundMusicExists = await fileExists(BACKGROUND_MUSIC_CONFIG.filePath);
    
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        service: 'Enhanced Video Generation Service',
        version: '2.1.0',
        backgroundMusic: {
            available: backgroundMusicExists,
            filePath: BACKGROUND_MUSIC_CONFIG.filePath,
            volume: `${(BACKGROUND_MUSIC_CONFIG.volume * 100)}%`,
            primaryAudioVolume: `${(BACKGROUND_MUSIC_CONFIG.primaryAudioVolume * 100)}%`,
            fadeInDuration: `${BACKGROUND_MUSIC_CONFIG.fadeInDuration}s`,
            fadeOutDuration: `${BACKGROUND_MUSIC_CONFIG.fadeOutDuration}s`
        },
        features: [
            '9:16 Vertical Video Support (432x768 Ultra Quality)',
            'ASS Subtitle Integration',
            'Karaoke-style Text Highlighting',
            'Auto Audio Duration Detection',
            'Automatic File Location by Filename',
            'Flexible Background Image Support',
            'Multi-Image Support with Zoom-Out Effects and Fade Transitions (a,b,c,d,e,f,g variants)',
            'Mobile-optimized Layout',
            'Background Music Support with Auto-looping',
            'Professional Audio Mixing with Fade Effects',
            'Volume-balanced Audio (Speech Priority)',
            'PCM to MP3 Audio Conversion',
            'Media File Management'
        ],
        endpoints: [
            'GET /health',
            'POST /create-enhanced-video',
            'POST /debug-files',
            'POST /validate-ass',
            'POST /convert-pcm-to-mp3',
            'GET /files'
        ]
    });
});

/**
 * Debug file locations
 */
app.post('/debug-files', async (req, res) => {
    try {
        const { audioFileName, assFileName, backgroundImage } = req.body;
        
        const paths = {
            audio: audioFileName ? {
                input: audioFileName,
                resolved: path.join(__dirname, 'shared-data', 'downloads', audioFileName),
                exists: audioFileName ? await fileExists(path.join(__dirname, 'shared-data', 'downloads', audioFileName)) : false
            } : null,
            ass: assFileName ? {
                input: assFileName,
                resolved: path.join(__dirname, 'shared-data', 'ass-scripts', assFileName),
                exists: assFileName ? await fileExists(path.join(__dirname, 'shared-data', 'ass-scripts', assFileName)) : false
            } : null,
            background: backgroundImage ? {
                input: backgroundImage,
                resolved: path.join(__dirname, 'shared-data', 'images', backgroundImage),
                exists: backgroundImage ? await fileExists(path.join(__dirname, 'shared-data', 'images', backgroundImage)) : false,
                alternativePaths: [
                    path.join('shared-data', 'images', backgroundImage),
                    path.join(__dirname, 'shared-data', 'images', path.basename(backgroundImage))
                ]
            } : null
        };
        
        // Check directory listings
        const directories = {};
        try {
            directories.downloads = await fs.readdir(path.join(__dirname, 'shared-data', 'downloads'));
        } catch (e) {
            directories.downloads = ['Error reading directory'];
        }
        
        try {
            directories.assScripts = await fs.readdir(path.join(__dirname, 'shared-data', 'ass-scripts'));
        } catch (e) {
            directories.assScripts = ['Error reading directory'];
        }
        
        try {
            directories.images = await fs.readdir(path.join(__dirname, 'shared-data', 'images'));
        } catch (e) {
            directories.images = ['Error reading directory'];
        }
        
        res.json({
            success: true,
            paths,
            directories,
            workingDirectory: __dirname
        });
        
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * Enhanced video creation endpoint with ASS subtitle support
 */
app.post('/create-enhanced-video', async (req, res) => {
    const requestId = Date.now().toString(36);
    console.log(`🎬 [${requestId}] Enhanced Video Creation Request:`, {
        body: Object.keys(req.body),
        timestamp: new Date().toISOString(),
        ip: req.ip
    });

    try {
        const {
            audioFileName,         // Just filename, e.g., "Chapter_1_Verse_1.mp3"
            assFileName,           // Just filename, e.g., "Chapter_1_Verse_1.ass"
            backgroundImage,       // Just filename, e.g., "Chapter_1_Verse_1.jpg"
            filename,              // Optional - will auto-generate from audio file if not provided
            videoWidth = VIDEO_CONFIG.MOBILE_PORTRAIT.width,
            videoHeight = VIDEO_CONFIG.MOBILE_PORTRAIT.height,
            quality = 'high',
            extraDuration = 5      // Extra seconds to add to video duration
        } = req.body;

        // Enhanced input validation
        if (!audioFileName) {
            return res.status(400).json({
                success: false,
                error: 'Missing required field: audioFileName',
                requestId
            });
        }

        // Validate and sanitize all inputs
        let sanitizedAudioFileName, sanitizedAssFileName, sanitizedBackgroundImage;
        
        try {
            sanitizedAudioFileName = validateAndSanitizeFilename(
                audioFileName, 
                PROCESSING_CONFIG.allowedAudioFormats, 
                'Audio file'
            );
            
            if (assFileName) {
                sanitizedAssFileName = validateAndSanitizeFilename(
                    assFileName, 
                    ['.ass', '.srt'], 
                    'Subtitle file'
                );
            }
            
            if (backgroundImage) {
                sanitizedBackgroundImage = validateAndSanitizeFilename(
                    backgroundImage, 
                    PROCESSING_CONFIG.allowedImageFormats, 
                    'Background image'
                );
            }
        } catch (validationError) {
            return res.status(400).json({
                success: false,
                error: `Input validation failed: ${validationError.message}`,
                requestId
            });
        }

        // Validate video dimensions
        if (isNaN(videoWidth) || isNaN(videoHeight) || videoWidth < 240 || videoHeight < 240 || 
            videoWidth > 4096 || videoHeight > 4096) {
            return res.status(400).json({
                success: false,
                error: 'Invalid video dimensions. Width and height must be between 240 and 4096 pixels.',
                requestId
            });
        }

        // Validate quality setting
        if (!['high', 'medium', 'low'].includes(quality)) {
            return res.status(400).json({
                success: false,
                error: 'Invalid quality setting. Must be: high, medium, or low',
                requestId
            });
        }

        // Validate extra duration
        const extraDurationNum = parseInt(extraDuration);
        if (isNaN(extraDurationNum) || extraDurationNum < 0 || extraDurationNum > 30) {
            return res.status(400).json({
                success: false,
                error: 'Invalid extraDuration. Must be between 0 and 30 seconds.',
                requestId
            });
        }

        // Check concurrent job limit
        if (runningProcesses.size >= PROCESSING_CONFIG.maxConcurrentJobs) {
            return res.status(429).json({
                success: false,
                error: `Server is busy processing ${runningProcesses.size} videos. Please try again later.`,
                requestId
            });
        }

        // Auto-generate video filename from audio file if not provided
        let videoFilename = filename;
        if (!videoFilename) {
            videoFilename = sanitizedAudioFileName.replace(/\.[^/.]+$/, '.mp4'); // Replace extension with .mp4
            console.log(`📝 [${requestId}] Auto-generated video filename: ${videoFilename}`);
        } else {
            // Validate provided filename
            try {
                videoFilename = validateAndSanitizeFilename(
                    videoFilename.endsWith('.mp4') ? videoFilename : videoFilename + '.mp4',
                    ['.mp4'],
                    'Output video'
                );
            } catch (validationError) {
                return res.status(400).json({
                    success: false,
                    error: `Output filename validation failed: ${validationError.message}`,
                    requestId
                });
            }
        }

        // Resolve file paths automatically using sanitized names
        const audioFilePath = path.join(__dirname, 'shared-data', 'downloads', sanitizedAudioFileName);
        const assFilePath = sanitizedAssFileName ? path.join(__dirname, 'shared-data', 'ass-scripts', sanitizedAssFileName) : null;

        console.log(`📁 [${requestId}] Resolved file paths:`, {
            audioFilePath,
            assFilePath,
            backgroundImage: sanitizedBackgroundImage
        });

        // Validate files exist and check sizes
        console.log(`🔍 [${requestId}] Validating input files...`);
        
        try {
            await validateFile(audioFilePath, PROCESSING_CONFIG.maxFileSize, 'Audio file');
            console.log(`✅ [${requestId}] Audio file validated: downloads/${sanitizedAudioFileName}`);

            if (assFilePath) {
                await validateFile(assFilePath, 1024 * 1024, 'Subtitle file'); // 1MB limit for ASS files
                console.log(`✅ [${requestId}] Subtitle file validated: ass-scripts/${sanitizedAssFileName}`);
            }
        } catch (fileError) {
            return res.status(400).json({
                success: false,
                error: fileError.message,
                requestId
            });
        }

        const result = await createEnhancedVerticalVideo({
            audioFilePath,
            assFilePath,
            backgroundImage: sanitizedBackgroundImage,
            filename: videoFilename,
            videoWidth: parseInt(videoWidth),
            videoHeight: parseInt(videoHeight),
            quality,
            extraDuration: extraDurationNum,
            requestId
        });

        console.log(`🎉 [${requestId}] Video creation completed successfully`);
        
        res.status(200)
           .header('Content-Type', 'application/json')
           .header('Access-Control-Allow-Origin', '*')
           .json({
            success: true,
            message: 'Enhanced video created successfully',
            requestId,
            ...result
        });

    } catch (error) {
        console.error(`❌ [${requestId}] Enhanced video creation failed:`, error);
        
        // Enhanced error response with categorization
        let statusCode = 500;
        let errorMessage = error.message;

        // Categorize errors for better client handling
        if (errorMessage.includes('timeout') || errorMessage.includes('timed out')) {
            statusCode = 408; // Request Timeout
        } else if (errorMessage.includes('not found') || errorMessage.includes('ENOENT')) {
            statusCode = 404; // Not Found
        } else if (errorMessage.includes('too large') || errorMessage.includes('size')) {
            statusCode = 413; // Payload Too Large
        } else if (errorMessage.includes('validation') || errorMessage.includes('Invalid')) {
            statusCode = 400; // Bad Request
        } else if (errorMessage.includes('overloaded') || errorMessage.includes('busy')) {
            statusCode = 503; // Service Unavailable
        }

        res.status(statusCode)
           .header('Content-Type', 'application/json')
           .header('Access-Control-Allow-Origin', '*')
           .json({
            success: false,
            error: errorMessage,
            requestId,
            timestamp: new Date().toISOString(),
            errorType: statusCode === 408 ? 'timeout' : 
                      statusCode === 404 ? 'not_found' : 
                      statusCode === 413 ? 'file_too_large' : 
                      statusCode === 400 ? 'validation_error' : 
                      statusCode === 503 ? 'server_busy' : 'processing_error'
        });
    }
});

// Global variable to track processing files
const processingFiles = new Set();

/**
 * Create enhanced vertical video with ASS subtitle support
 */
async function createEnhancedVerticalVideo({
    audioFilePath,
    assFilePath,
    backgroundImage,
    filename,
    videoWidth,
    videoHeight,
    quality,
    extraDuration,
    requestId = 'unknown'
}) {
    const videosDir = path.join(__dirname, 'shared-data', 'videos');
    const tempDir = path.join(__dirname, 'shared-data', 'temp');
    const imagesDir = path.join(__dirname, 'shared-data', 'images');

    // Ensure filename doesn't have double .mp4 extension
    const cleanFilename = filename.endsWith('.mp4') ? filename.slice(0, -4) : filename;
    const outputVideoPath = path.join(videosDir, `${cleanFilename}.mp4`);

    // Check if this file is already being processed
    if (processingFiles.has(outputVideoPath)) {
        throw new Error(`Video ${cleanFilename}.mp4 is already being processed. Please wait for completion.`);
    }

    // Add to processing set
    processingFiles.add(outputVideoPath);
    console.log(`🔒 [${requestId}] File lock acquired for: ${cleanFilename}.mp4`);

    try {
        // Ensure directories exist
        await fs.mkdir(videosDir, { recursive: true });
        await fs.mkdir(tempDir, { recursive: true });
        await fs.mkdir(imagesDir, { recursive: true });

        // Resolve file paths
        const resolvedAudioPath = path.resolve(audioFilePath);

        console.log('📂 File paths:', {
            resolvedAudioPath,
            assFilePath: assFilePath || 'none',
            backgroundImage: backgroundImage || 'default fallback',
            outputVideoPath
        });

        // Get audio duration with timeout protection
        let exactDuration;
        try {
            const probeCommand = `ffprobe -v quiet -show_entries format=duration -of csv=p=0 "${resolvedAudioPath}"`;
            const { stdout } = await executeFFmpegWithTimeout(probeCommand, `${requestId}-probe`, 30000); // 30 second timeout for probe
            const audioDuration = parseFloat(stdout.trim());
            
            if (isNaN(audioDuration) || audioDuration <= 0) {
                throw new Error('Invalid audio duration detected');
            }
            
            exactDuration = audioDuration + extraDuration;
            console.log(`🎵 [${requestId}] Audio duration: ${audioDuration}s`);
            console.log(`🎬 [${requestId}] Video duration (with +${extraDuration}s extra): ${exactDuration}s`);
        } catch (error) {
            console.error(`⚠️ [${requestId}] Could not detect audio duration:`, error.message);
            throw new Error(`Failed to detect audio duration: ${error.message}`);
        }

        // Prepare background image with improved path checking and multi-image support
        const backgroundPath = await prepareBackgroundImage(backgroundImage, videoWidth, videoHeight, tempDir, imagesDir, exactDuration, requestId);

        // Build FFmpeg command for enhanced vertical video
        const ffmpegCommand = await buildEnhancedFFmpegCommand({
            backgroundPath,
            resolvedAudioPath,
            assFilePath,
            outputVideoPath,
            videoWidth,
            videoHeight,
            exactDuration,
            quality,
            extraDuration
        });

        console.log(`🎬 [${requestId}] FFmpeg Command prepared, starting execution...`);

        // Execute video creation with timeout protection
        const startTime = Date.now();
        try {
            const result = await executeFFmpegWithTimeout(ffmpegCommand, requestId, PROCESSING_CONFIG.timeout);
            const processingTime = Date.now() - startTime;

            console.log(`✅ [${requestId}] Video created successfully in ${(processingTime / 1000).toFixed(2)}s`);

            // Validate video file integrity
            await validateVideoFile(outputVideoPath);

            // Get video file stats
            const stats = await fs.stat(outputVideoPath);

            return {
                videoPath: `/videos/${filename}`,
                fullPath: outputVideoPath,
                audioDuration: exactDuration - extraDuration,
                videoDuration: exactDuration,
                extraDuration: extraDuration,
                fileSize: `${(stats.size / (1024 * 1024)).toFixed(2)} MB`,
                dimensions: `${videoWidth}x${videoHeight}`,
                processingTime: `${(processingTime / 1000).toFixed(2)}s`,
                backgroundUsed: backgroundImage || 'fallback',
                filesFound: {
                    audio: `downloads/${path.basename(audioFilePath)}`,
                    subtitle: assFilePath ? `ass-scripts/${path.basename(assFilePath)}` : 'none',
                    background: backgroundImage || 'fallback'
                }
            };

        } catch (error) {
            console.error(`❌ [${requestId}] FFmpeg execution failed:`, error);
            throw new Error(`Video creation failed: ${error.message}`);
        }
    } finally {
        // Always remove from processing set and cleanup temp files
        processingFiles.delete(outputVideoPath);
        console.log(`🔓 [${requestId}] File lock released for: ${cleanFilename}.mp4`);
        
        // Clean up any temporary files created during this process
        await cleanupTempFiles();
    }
}

/**
 * Build enhanced FFmpeg command with ASS subtitle support, multi-image background, and background music
 */
async function buildEnhancedFFmpegCommand({
    backgroundPath,
    resolvedAudioPath,
    assFilePath,
    outputVideoPath,
    videoWidth,
    videoHeight,
    exactDuration,
    quality,
    extraDuration
}) {
    const config = VIDEO_CONFIG.ENCODING;
    // Ultra-fast encoding settings optimized for 432x768 YouTube Shorts
    const crfValue = quality === 'high' ? 20 : quality === 'medium' ? 25 : 30;  // Adjusted CRF for faster processing
    
    // Optimized quality parameters for faster processing
    const maxBitrate = quality === 'high' ? '1500k' : quality === 'medium' ? '1200k' : '800k';
    const bufferSize = quality === 'high' ? '3000k' : quality === 'medium' ? '2400k' : '1600k';

    // Check if background is a video or image
    const isVideoBackground = backgroundPath.endsWith('.mp4') || backgroundPath.endsWith('.mov') || backgroundPath.endsWith('.avi');
    
    // Check if background music exists
    const backgroundMusicExists = await fileExists(BACKGROUND_MUSIC_CONFIG.filePath);
    
    // Build command as a string to properly handle complex filters
    let command;
    let inputIndex = 0;
    
    // Input 0: Background image/video
    if (isVideoBackground) {
        command = `ffmpeg -y -i "${backgroundPath}"`;
        console.log('🎞️ Using video background with transitions');
    } else {
        command = `ffmpeg -y -loop 1 -i "${backgroundPath}"`;
        console.log('🖼️ Using static image background');
    }
    inputIndex++; // Now inputIndex = 1
    
    // Input 1: Primary audio (speech)
    command += ` -i "${resolvedAudioPath}"`;
    inputIndex++; // Now inputIndex = 2
    
    // Input 2: Background music (if available)
    if (backgroundMusicExists) {
        command += ` -stream_loop -1 -i "${BACKGROUND_MUSIC_CONFIG.filePath}"`;
        console.log('🎵 Adding background music with looping support');
        inputIndex++; // Now inputIndex = 3
    }

    // Build filter complex chain
    let filterChain = '';
    
    // Video processing (background scaling and subtitles)
    if (assFilePath && await fileExists(assFilePath)) {
        console.log('📝 Adding ASS subtitle support');
        console.log(`🎯 ASS file: ${assFilePath}`);
        
        const resolvedAssPath = path.resolve(assFilePath);
        
        // Verify ASS file is readable and has content
        try {
            const assContent = await fs.readFile(resolvedAssPath, 'utf8');
            const dialogueLines = assContent.split('\n').filter(line => line.startsWith('Dialogue:')).length;
            console.log(`📊 ASS file loaded: ${dialogueLines} dialogue lines found`);
            
            if (dialogueLines === 0) {
                console.log('⚠️ ASS file has no dialogue lines, creating video without subtitles');
                filterChain += `[0:v]scale=${videoWidth}:${videoHeight}:force_original_aspect_ratio=decrease:flags=lanczos,pad=${videoWidth}:${videoHeight}:color=black[video];`;
            } else {
                // Ultra-high quality subtitle rendering optimized for 432x768 YouTube Shorts
                const escapedAssPath = resolvedAssPath.replace(/'/g, "'\"'\"'");
                // Optimized font size and styling for 432x768 resolution
                const subtitleStyle = 'FontSize=24,Outline=3,Shadow=2,BackColour=&H80000000,PrimaryColour=&HFFFFFF,Bold=1';
                filterChain += `[0:v]scale=${videoWidth}:${videoHeight}:force_original_aspect_ratio=decrease:flags=lanczos,pad=${videoWidth}:${videoHeight}:color=black[bg];`;
                filterChain += `[bg]subtitles='${escapedAssPath}':fontsdir='/usr/share/fonts/truetype/noto':force_style='${subtitleStyle}'[video];`;
                console.log('✅ ASS subtitle filter added to video processing');
            }
        } catch (error) {
            console.log('⚠️ Could not read ASS file content:', error.message);
            console.log('📝 Creating video without subtitles');
            filterChain += `[0:v]scale=${videoWidth}:${videoHeight}:force_original_aspect_ratio=decrease:flags=lanczos,pad=${videoWidth}:${videoHeight}:color=black[video];`;
        }
    } else {
        if (assFilePath) {
            console.log(`⚠️ ASS file not found: ${assFilePath}`);
        } else {
            console.log('📝 No ASS file provided');
        }
        console.log('📝 Creating video without subtitles');
        filterChain += `[0:v]scale=${videoWidth}:${videoHeight}:force_original_aspect_ratio=decrease:flags=lanczos,pad=${videoWidth}:${videoHeight}:color=black[video];`;
    }

    // Audio processing with background music
    if (backgroundMusicExists) {
        console.log('🎶 Adding professional audio mixing with background music');
        
        // Audio processing chain with background music
        // 1. Primary audio (speech) - pad to full video duration, then apply volume
        // 2. Background music - extend to full video duration with fade effects
        // 3. Mix both audios together for the full video duration
        
        const fadeInDur = BACKGROUND_MUSIC_CONFIG.fadeInDuration;
        const fadeOutDur = BACKGROUND_MUSIC_CONFIG.fadeOutDuration;
        const bgVolume = BACKGROUND_MUSIC_CONFIG.volume;
        const primaryVolume = BACKGROUND_MUSIC_CONFIG.primaryAudioVolume;
        
        // Process primary audio: pad to full video duration, then apply volume
        // This ensures the main audio is extended with silence for the extra duration
        filterChain += `[1:a]apad=pad_dur=${exactDuration},volume=${primaryVolume}[speech];`;
        
        // Process background music: trim to video duration, add fades, adjust volume
        filterChain += `[2:a]atrim=0:${exactDuration},asetpts=PTS-STARTPTS,`;
        filterChain += `afade=t=in:st=0:d=${fadeInDur},`;
        filterChain += `afade=t=out:st=${Math.max(0, exactDuration - fadeOutDur)}:d=${fadeOutDur},`;
        filterChain += `volume=${bgVolume}[bgmusic];`;
        
        // Mix padded primary audio with background music for full duration
        // This ensures BGM continues playing during the extra 5 seconds
        filterChain += `[speech][bgmusic]amix=inputs=2:duration=longest:weights=${primaryVolume}|${bgVolume}[audio]`;
        
        console.log(`🎵 Background music configured: Speech ${(primaryVolume * 100)}%, BGM ${(bgVolume * 100)}%, Duration ${exactDuration}s`);
    } else {
        console.log('⚠️ Background music file not found, using original audio only');
        // Use original audio without background music
        filterChain += `[1:a]apad=pad_dur=${exactDuration}[audio]`;
    }

    // Add the complete filter complex
    command += ` -filter_complex "${filterChain}"`;
    
    // Map the final outputs
    command += ` -map "[video]" -map "[audio]"`;
    
    // Add encoding settings optimized for speed
    command += ` -c:v ${config.videoCodec}`;
    command += ` -c:a ${config.audioCodec}`;
    command += ` -crf ${crfValue}`;
    command += ` -preset ${config.preset}`;
    command += ` -profile:v ${config.profile}`;
    command += ` -tune ${config.tune}`;        // Add zerolatency tuning for faster encoding
    command += ` -maxrate ${maxBitrate}`;      // Maximum bitrate constraint
    command += ` -bufsize ${bufferSize}`;      // Buffer size for rate control
    command += ` -b:a ${config.audioBitrate}`; // Optimized audio bitrate
    command += ` -level ${config.level}`;
    command += ` -pix_fmt ${config.pixelFormat}`;
    command += ` -r 30`;                       // 30fps for smooth motion
    command += ` -g 60`;                       // Increased GOP size for faster encoding
    command += ` -bf 1`;                       // Reduced B-frames for faster processing
    command += ` -refs 2`;                     // Reduced reference frames for speed
    command += ` -threads 0`;                  // Use all available CPU threads
    command += ` -t ${exactDuration}`;
    command += ` -shortest`;
    command += ` -ar 44100`;                   // Audio sample rate
    command += ` -movflags +faststart`;        // Enable fast start for web playback
    command += ` -metadata title="Bhagavad Gita Verse"`;
    command += ` -metadata comment="Generated with Enhanced Video Service - Optimized 432x768 with Background Music"`;
    command += ` "${outputVideoPath}"`;

    return command;
}

/**
 * Find all available images for a base name (main + variants a, b, c, d, e, f, g)
 */
async function findAvailableImages(backgroundImage, imagesDir) {
    if (!backgroundImage) return [];
    
    const imageBaseName = backgroundImage.replace(/\.[^/.]+$/, ''); // Remove extension
    const imageExtension = path.extname(backgroundImage);
    const variants = ['', 'a', 'b', 'c', 'd', 'e', 'f', 'g']; // Main image + variants
    const availableImages = [];
    
    console.log(`🔍 Searching for image variants of: ${imageBaseName}`);
    
    for (const variant of variants) {
        const imageName = `${imageBaseName}${variant}${imageExtension}`;
        const possiblePaths = [
            path.join(imagesDir, imageName),
            path.join(__dirname, 'shared-data', 'images', imageName)
        ];
        
        for (const possiblePath of possiblePaths) {
            if (await fileExists(possiblePath)) {
                availableImages.push({
                    path: possiblePath,
                    name: imageName,
                    variant: variant || 'main'
                });
                console.log(`✅ Found image variant: ${imageName}`);
                break;
            }
        }
    }
    
    console.log(`📊 Found ${availableImages.length} image variants`);
    return availableImages;
}

/**
 * Create video with multiple images and transitions with zoom-out effects
 */
async function createMultiImageVideo(images, duration, videoWidth, videoHeight, tempDir, requestId = 'unknown') {
    if (images.length === 0) {
        throw new Error('No images provided for video creation');
    }
    
    if (images.length === 1) {
        // Single image with zoom-out effect
        return await createSingleImageWithZoom(images[0], duration, videoWidth, videoHeight, tempDir, requestId);
    }
    
    const outputVideoPath = path.join(tempDir, `multi-image-background-${requestId}.mp4`);
    const transitionDuration = 0.5; // 0.5 seconds for each transition
    const imageDuration = (duration - (images.length - 1) * transitionDuration) / images.length;
    
    console.log(`🎬 [${requestId}] Creating multi-image video with ${images.length} images and zoom-out effects`);
    console.log(`⏱️ [${requestId}] Each image duration: ${imageDuration.toFixed(2)}s, transition: ${transitionDuration}s`);
    
    // Build FFmpeg filter for multiple images with crossfade transitions and zoom-out effects
    let filterComplex = '';
    let inputMaps = '';
    
    // Add all images as inputs
    for (let i = 0; i < images.length; i++) {
        inputMaps += ` -loop 1 -t ${imageDuration + transitionDuration} -i "${images[i].path}"`;
    }
    
    // Scale all images with zoom-out effect
    for (let i = 0; i < images.length; i++) {
        // Apply single zoom-out effect: start at 120% scale and zoom out to 100% over the entire image duration
        // Calculate zoom rate based on image duration for one complete zoom-out
        const totalFrames = Math.round((imageDuration + transitionDuration) * 30); // 30 FPS
        const zoomRate = 0.2 / totalFrames; // 0.2 = difference between 1.2 and 1.0, distributed over all frames
        
        filterComplex += `[${i}:v]scale=${Math.round(videoWidth * 1.2)}:${Math.round(videoHeight * 1.2)}:force_original_aspect_ratio=decrease,`;
        filterComplex += `pad=${Math.round(videoWidth * 1.2)}:${Math.round(videoHeight * 1.2)}:color=black,`;
        filterComplex += `zoompan=z='1.2-${zoomRate}*on':d=${totalFrames}:x='iw/2-(iw/zoom/2)':y='ih/2-(ih/zoom/2)':s=${videoWidth}x${videoHeight},`;
        filterComplex += `setpts=PTS-STARTPTS[v${i}];`;
    }
    
    // Create crossfade transitions between images
    let currentOutput = 'v0';
    for (let i = 1; i < images.length; i++) {
        const offset = (imageDuration * i) + (transitionDuration * (i - 1));
        filterComplex += `[${currentOutput}][v${i}]xfade=transition=fade:duration=${transitionDuration}:offset=${offset}[vout${i}];`;
        currentOutput = `vout${i}`;
    }
    
    // Remove the trailing semicolon
    filterComplex = filterComplex.slice(0, -1);
    
    const ffmpegCommand = `ffmpeg -y${inputMaps} -filter_complex "${filterComplex}" -map "[${currentOutput}]" -t ${duration} -c:v libx264 -preset medium -crf 23 -pix_fmt yuv420p -r 30 "${outputVideoPath}"`;
    
    console.log(`🎬 [${requestId}] Multi-image FFmpeg command prepared`);
    
    try {
        await executeFFmpegWithTimeout(ffmpegCommand, `${requestId}-multiimg`, 180000); // 3 minute timeout for multi-image
        console.log(`✅ [${requestId}] Multi-image video with zoom-out effects created successfully`);
        return outputVideoPath;
    } catch (error) {
        console.error(`❌ [${requestId}] Failed to create multi-image video with zoom-out effects:`, error.message);
        throw new Error(`Multi-image video creation failed: ${error.message}`);
    }
}

/**
 * Create single image video with zoom-out effect
 */
async function createSingleImageWithZoom(image, duration, videoWidth, videoHeight, tempDir, requestId = 'unknown') {
    const outputVideoPath = path.join(tempDir, `single-image-zoom-background-${requestId}.mp4`);
    
    console.log(`🎬 [${requestId}] Creating single image video with one zoom-out effect`);
    console.log(`⏱️ [${requestId}] Duration: ${duration.toFixed(2)}s, Image: ${image.name}`);
    
    // Create single zoom-out effect: start at 120% scale and zoom out to 100% over the entire duration
    const totalFrames = Math.round(duration * 30); // 30 FPS
    const zoomRate = 0.2 / totalFrames; // 0.2 = difference between 1.2 and 1.0, distributed over all frames
    
    const ffmpegCommand = `ffmpeg -y -loop 1 -i "${image.path}" -filter_complex "[0:v]scale=${Math.round(videoWidth * 1.2)}:${Math.round(videoHeight * 1.2)}:force_original_aspect_ratio=decrease,pad=${Math.round(videoWidth * 1.2)}:${Math.round(videoHeight * 1.2)}:color=black,zoompan=z='1.2-${zoomRate}*on':d=${totalFrames}:x='iw/2-(iw/zoom/2)':y='ih/2-(ih/zoom/2)':s=${videoWidth}x${videoHeight}" -t ${duration} -c:v libx264 -preset medium -crf 23 -pix_fmt yuv420p -r 30 "${outputVideoPath}"`;
    
    console.log(`🎬 [${requestId}] Single image zoom FFmpeg command prepared`);
    
    try {
        await executeFFmpegWithTimeout(ffmpegCommand, `${requestId}-singleimg`, 120000); // 2 minute timeout for single image
        console.log(`✅ [${requestId}] Single image video with one zoom-out effect created successfully`);
        return outputVideoPath;
    } catch (error) {
        console.error(`❌ [${requestId}] Failed to create single image video with zoom-out effect:`, error.message);
        throw new Error(`Single image zoom video creation failed: ${error.message}`);
    }
}

/**
 * Prepare background image for vertical format (ENHANCED WITH MULTI-IMAGE SUPPORT)
 */
async function prepareBackgroundImage(backgroundImage, videoWidth, videoHeight, tempDir, imagesDir, videoDuration, requestId = 'unknown') {
    let backgroundPath;

    if (backgroundImage) {
        // Find all available image variants
        const availableImages = await findAvailableImages(backgroundImage, imagesDir);
        
        if (availableImages.length > 0) {
            if (availableImages.length === 1) {
                // Single image found
                backgroundPath = availableImages[0].path;
                console.log(`🖼️ [${requestId}] Using single background image: ${availableImages[0].name}`);
            } else {
                // Multiple images found - create video with transitions
                console.log(`🎞️ [${requestId}] Creating video with ${availableImages.length} images and transitions`);
                backgroundPath = await createMultiImageVideo(
                    availableImages, 
                    videoDuration, 
                    videoWidth, 
                    videoHeight, 
                    tempDir,
                    requestId
                );
                // Track the created video file for cleanup
                addTempFile(backgroundPath);
            }
        } else {
            // No images found, create fallback
            console.log(`⚠️ [${requestId}] Background image "${backgroundImage}" not found in any location`);
            console.log(`🎨 [${requestId}] Creating simple fallback background...`);
            backgroundPath = addTempFile(path.join(tempDir, `simple-bg-${requestId}.jpg`));
            await createSimpleFallbackBackground(backgroundPath, videoWidth, videoHeight);
        }
    } else {
        // No specific image provided, use fallback
        console.log(`📝 [${requestId}] No background image specified, creating simple fallback`);
        backgroundPath = addTempFile(path.join(tempDir, `simple-bg-${requestId}.jpg`));
        await createSimpleFallbackBackground(backgroundPath, videoWidth, videoHeight);
    }

    return backgroundPath;
}

/**
 * Validate video file integrity
 */
async function validateVideoFile(videoPath) {
    console.log('🔍 Validating video file integrity...');
    
    try {
        // Use ffprobe to validate the video file
        const { stdout } = await execAsync(`ffprobe -v quiet -print_format json -show_format -show_streams "${videoPath}"`);
        const videoInfo = JSON.parse(stdout);
        
        // Check if video has both video and audio streams
        const hasVideo = videoInfo.streams.some(stream => stream.codec_type === 'video');
        const hasAudio = videoInfo.streams.some(stream => stream.codec_type === 'audio');
        
        if (!hasVideo) {
            throw new Error('Video file is missing video stream');
        }
        
        if (!hasAudio) {
            console.log('⚠️ Warning: Video file is missing audio stream');
        }
        
        // Check video duration
        const duration = parseFloat(videoInfo.format.duration);
        if (duration < 1) {
            throw new Error('Video duration is too short (less than 1 second)');
        }
        
        console.log('✅ Video file validation passed');
        console.log(`📊 Video info: ${duration.toFixed(2)}s, ${videoInfo.streams.length} streams`);
        
    } catch (error) {
        console.error('❌ Video validation failed:', error.message);
        throw new Error(`Video file is corrupted or invalid: ${error.message}`);
    }
}

/**
 * Create simple solid color fallback background
 */
async function createSimpleFallbackBackground(outputPath, width, height) {
    console.log('🎨 Creating simple fallback background...');
    
    // Create a simple solid color background using FFmpeg
    const fallbackCommand = `ffmpeg -y -f lavfi -i color=c=0x1e3c72:size=${width}x${height}:duration=1 -frames:v 1 "${outputPath}"`;
    
    try {
        await execAsync(fallbackCommand);
        console.log('✅ Simple fallback background created');
    } catch (error) {
        console.error('❌ Failed to create fallback background:', error.message);
        throw new Error('Failed to create background image');
    }
}

/**
 * PCM to MP3 conversion endpoint
 * Based on the original Python implementation pattern
 */
app.post('/convert-pcm-to-mp3', async (req, res) => {
    const requestId = Date.now().toString(36);
    
    // Handle both query parameters and request body for input/output file names
    const inputFileName = req.query.inputFile || req.body?.inputFile || 'out.pcm';
    const outputFileName = req.query.outputFile || req.body?.outputFile || req.query.output_name || req.body?.output_name || 'out.mp3';
    const timestamped = (req.query.timestamped || req.body?.timestamped || 'false').toLowerCase() === 'true';
    
    console.log(`🎵 [${requestId}] PCM to MP3 Conversion Request:`, {
        timestamp: new Date().toISOString(),
        ip: req.ip,
        inputFileName,
        outputFileName,
        timestamped
    });

    try {
        // Validate input file name
        if (inputFileName && typeof inputFileName !== 'string') {
            return res.status(400).json({
                success: false,
                error: 'inputFile must be a string',
                requestId,
                timestamp: new Date().toISOString()
            });
        }

        // Validate output file name
        if (outputFileName && typeof outputFileName !== 'string') {
            return res.status(400).json({
                success: false,
                error: 'outputFile must be a string',
                requestId,
                timestamp: new Date().toISOString()
            });
        }

        // Ensure output file has .mp3 extension
        const outputFileWithExt = outputFileName.endsWith('.mp3') ? outputFileName : `${outputFileName}.mp3`;
        
        // Add timestamp if requested
        const finalOutputName = timestamped 
            ? outputFileWithExt.replace('.mp3', `-${new Date().toISOString().replace(/[:.]/g, '-')}.mp3`)
            : outputFileWithExt;
        
        // Define file paths
        const inputFile = path.join(__dirname, 'shared-data', 'downloads', inputFileName);
        const outputFile = path.join(__dirname, 'shared-data', 'downloads', finalOutputName);

        // Handle binary data in request body (if present)
        if (req.body && Buffer.isBuffer(req.body) && req.body.length > 0) {
            console.log(`🎵 [${requestId}] Writing binary PCM data to file:`, {
                dataSize: `${(req.body.length / 1024).toFixed(2)} KB`
            });
            await fs.writeFile(inputFile, req.body);
        }

        // Check if input file exists
        if (!await fileExists(inputFile)) {
            return res.status(404).json({
                success: false,
                error: 'Input PCM file not found',
                details: 'Expected file: shared-data/downloads/out.pcm or binary data in request body',
                requestId,
                timestamp: new Date().toISOString()
            });
        }

        // Get input file stats
        const inputStats = await fs.stat(inputFile);
        const inputSize = inputStats.size;

        console.log(`🎵 [${requestId}] Converting PCM to MP3:`, {
            input: inputFileName,
            output: finalOutputName,
            inputSize: `${(inputSize / 1024 / 1024).toFixed(2)} MB`
        });

        // Build FFmpeg command (matching Python implementation)
        const ffmpegCommand = `ffmpeg -y -f s16le -ar 24000 -ac 1 -i "${inputFile}" "${outputFile}"`;

        // Execute conversion with timeout
        const startTime = Date.now();
        await executeFFmpegWithTimeout(ffmpegCommand, `${requestId}-pcm`, 60000);
        const processingTime = Date.now() - startTime;

        // Validate output file was created
        if (!await fileExists(outputFile)) {
            throw new Error('Output MP3 file was not created');
        }

        // Get output file stats
        const outputStats = await fs.stat(outputFile);
        const outputSize = outputStats.size;
        const compressionRatio = inputSize > 0 ? (inputSize / outputSize) : 0;

        console.log(`✅ [${requestId}] PCM conversion completed successfully in ${(processingTime / 1000).toFixed(2)}s`);

        // Response format matching Python implementation
        const response = {
            success: true,
            message: 'PCM to MP3 conversion completed successfully',
            requestId,
            input_file: inputFile,
            output_file: outputFile,
            input_size_bytes: inputSize,
            output_size_bytes: outputSize,
            compression_ratio: Math.round(compressionRatio * 100) / 100,
            processing_time: `${(processingTime / 1000).toFixed(2)}s`,
            timestamp: new Date().toISOString()
        };

        res.status(200).json(response);

    } catch (error) {
        console.error(`❌ [${requestId}] PCM conversion failed:`, error);
        
        // Error categorization
        let statusCode = 500;
        if (error.message.includes('timeout') || error.message.includes('timed out')) {
            statusCode = 408;
        } else if (error.message.includes('not found') || error.message.includes('ENOENT')) {
            statusCode = 404;
        }

        res.status(statusCode).json({
            success: false,
            error: error.message,
            requestId,
            timestamp: new Date().toISOString(),
            error_type: statusCode === 408 ? 'timeout' : 
                       statusCode === 404 ? 'not_found' : 'processing_error'
        });
    }
});

/**
 * List media files endpoint
 */
app.get('/files', async (req, res) => {
    try {
        const mediaPath = path.join(__dirname, 'shared-data', 'downloads');
        
        if (!await fileExists(mediaPath)) {
            return res.status(404).json({
                success: false,
                error: 'Media directory not found',
                path: mediaPath
            });
        }

        const files = await fs.readdir(mediaPath);
        const fileDetails = await Promise.all(
            files.map(async (filename) => {
                const filePath = path.join(mediaPath, filename);
                const stats = await fs.stat(filePath);
                
                if (stats.isFile()) {
                    return {
                        name: filename,
                        sizeBytes: stats.size,
                        sizeMB: Math.round((stats.size / 1024 / 1024) * 100) / 100,
                        modified: stats.mtime.toISOString(),
                        extension: path.extname(filename).toLowerCase()
                    };
                }
                return null;
            })
        );

        const validFiles = fileDetails.filter(file => file !== null);

        res.json({
            success: true,
            mediaPath,
            fileCount: validFiles.length,
            files: validFiles,
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// 404 handler
app.use((req, res) => {
    res.status(404).json({
        error: 'Endpoint not found',
        availableEndpoints: [
            'GET /health',
            'POST /create-enhanced-video',
            'POST /debug-files'
        ]
    });
});

// Start server
app.listen(PORT, () => {
    console.log('🚀 Enhanced Video Generation Service Started');
    console.log(`📱 Server running on: http://localhost:${PORT}`);
    console.log(`🔗 Health check: http://localhost:${PORT}/health`);
    console.log(`🎬 Enhanced video endpoint: http://localhost:${PORT}/create-enhanced-video`);
    console.log(`🎵 PCM to MP3 conversion: http://localhost:${PORT}/convert-pcm-to-mp3`);
    console.log(`📁 File management: http://localhost:${PORT}/files`);
    console.log('');
    console.log('✨ Features:');
    console.log('  • 9:16 Vertical Video Support (432x768)');
    console.log('  • ASS Subtitle Integration with Karaoke Highlighting');
    console.log('  • Automatic File Location by Filename');
    console.log('  • Enhanced Background Image Processing');
    console.log('  • Multi-Image Support with Zoom-Out Effects and Fade Transitions (a,b,c,d,e,f,g variants)');
    console.log('  • Background Music Support with Auto-looping');
    console.log('  • Professional Audio Mixing (80% background music volume)');
    console.log('  • Fade-in/Fade-out Effects for Smooth Audio Transitions');
    console.log('  • PCM to MP3 Audio Conversion');
    console.log('  • Media File Management and Listing');
    console.log('');
});

module.exports = app;
