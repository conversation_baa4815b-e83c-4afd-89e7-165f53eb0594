#!/usr/bin/env python3
"""
Google Vertex AI Imagen Image Generation Script

This script uses Google Cloud service account credentials to generate images
using the Vertex AI Imagen model and saves them as PNG files.
"""

import json
import base64
import os
import sys
from typing import Dict, Any, Optional
import requests
from google.auth.transport.requests import Request
from google.oauth2 import service_account
from PIL import Image
import io


class VertexAIImageGenerator:
    """A class to handle Google Vertex AI Imagen image generation."""
    
    def __init__(self, credentials_path: str, project_id: str):
        """
        Initialize the image generator with credentials and project ID.
        
        Args:
            credentials_path: Path to the service account JSON file
            project_id: Google Cloud project ID
        """
        self.credentials_path = credentials_path
        self.project_id = project_id
        self.credentials = None
        self.access_token = None
        
        # Define the scope for Google Cloud Platform
        self.scopes = ['https://www.googleapis.com/auth/cloud-platform']
        
        # Vertex AI Imagen endpoint
        self.endpoint_url = (
            f"https://us-central1-aiplatform.googleapis.com/v1/projects/{project_id}/"
            f"locations/us-central1/publishers/google/models/imagen-4.0-generate-preview-06-06:predict"
        )
    
    def load_credentials(self) -> None:
        """Load and validate Google Cloud service account credentials."""
        try:
            if not os.path.exists(self.credentials_path):
                raise FileNotFoundError(f"Credentials file not found: {self.credentials_path}")
            
            # Load service account credentials
            self.credentials = service_account.Credentials.from_service_account_file(
                self.credentials_path,
                scopes=self.scopes
            )
            
            print(f"✓ Successfully loaded credentials from {self.credentials_path}")
            
        except Exception as e:
            print(f"✗ Error loading credentials: {e}")
            sys.exit(1)
    
    def generate_access_token(self) -> str:
        """Generate OAuth 2.0 access token using service account credentials."""
        try:
            if not self.credentials:
                raise ValueError("Credentials not loaded. Call load_credentials() first.")
            
            # Refresh the credentials to get a new access token
            self.credentials.refresh(Request())
            self.access_token = self.credentials.token
            
            if not self.access_token:
                raise ValueError("Failed to generate access token")
            
            print("✓ Successfully generated OAuth 2.0 access token")
            return self.access_token
            
        except Exception as e:
            print(f"✗ Error generating access token: {e}")
            sys.exit(1)
    
    def create_request_payload(self, prompt: str, sample_count: int = 1, seed: int = 42) -> Dict[str, Any]:
        """
        Create the request payload for the Vertex AI Imagen API.
        
        Args:
            prompt: Text prompt for image generation
            sample_count: Number of images to generate
            seed: Random seed for reproducible results
            
        Returns:
            Dictionary containing the request payload
        """
        payload = {
            "instances": [
                {
                    "prompt": prompt
                }
            ],
            "parameters": {
                "sampleCount": sample_count,
                "seed": seed,
                "addWatermark": False  # Disable watermark to allow seed parameter
            }
        }
        
        return payload
    
    def send_generation_request(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Send POST request to Vertex AI Imagen endpoint.
        
        Args:
            payload: Request payload dictionary
            
        Returns:
            Response data as dictionary
        """
        try:
            # Headers as specified in official documentation
            headers = {
                "Authorization": f"Bearer {self.access_token}",
                "Content-Type": "application/json; charset=utf-8"
            }
            
            print(f"📡 Sending request to Vertex AI Imagen API...")
            print(f"   Endpoint: {self.endpoint_url}")
            print(f"   Prompt: {payload['instances'][0]['prompt']}")
            print(f"   Sample Count: {payload['parameters']['sampleCount']}")
            print(f"   Seed: {payload['parameters']['seed']}")
            
            response = requests.post(
                self.endpoint_url,
                headers=headers,
                json=payload,
                timeout=120  # Increased timeout for image generation
            )
            
            if response.status_code == 200:
                print("✓ Successfully received response from Vertex AI")
                return response.json()
            else:
                print(f"✗ API request failed with status code: {response.status_code}")
                print(f"   Response headers: {dict(response.headers)}")
                print(f"   Response body: {response.text}")
                response.raise_for_status()
                
        except requests.exceptions.RequestException as e:
            print(f"✗ Request error: {e}")
            raise
        except Exception as e:
            print(f"✗ Unexpected error during API request: {e}")
            raise
    
    def extract_and_save_image(self, response_data: Dict[str, Any], output_filename: str = "output.png") -> str:
        """
        Extract base64 image from API response and save as PNG file.
        According to official documentation, the response format is:
        {
            "predictions": [
                {
                    "bytesBase64Encoded": "BASE64_IMG_BYTES",
                    "mimeType": "image/png"
                }
            ]
        }
        
        Args:
            response_data: Response data from Vertex AI API
            output_filename: Name of the output PNG file
            
        Returns:
            Path to the saved image file
        """
        try:
            # Print the full response for debugging
            print(f"📋 Full API response: {json.dumps(response_data, indent=2)}")
            
            # Extract predictions from response
            if 'predictions' not in response_data:
                raise ValueError("No 'predictions' key found in response")
            
            predictions = response_data['predictions']
            if not predictions:
                raise ValueError("No predictions found in response")
            
            # Get the first prediction (assuming single image generation)
            prediction = predictions[0]
            print(f"📋 First prediction keys: {list(prediction.keys())}")
            
            # According to official docs, the key should be 'bytesBase64Encoded'
            if 'bytesBase64Encoded' not in prediction:
                # If not found, try alternative keys and show what's available
                print(f"⚠️  'bytesBase64Encoded' not found. Available keys: {list(prediction.keys())}")
                
                # Try alternative keys
                possible_keys = ['image', 'generatedImage', 'data', 'base64_image']
                base64_image = None
                for key in possible_keys:
                    if key in prediction:
                        base64_image = prediction[key]
                        print(f"✓ Found image data in key: {key}")
                        break
                
                if not base64_image:
                    raise ValueError("Could not find base64 image data in any expected key")
            else:
                base64_image = prediction['bytesBase64Encoded']
                print("✓ Found image data in 'bytesBase64Encoded' key")
            
            # Show mime type if available
            if 'mimeType' in prediction:
                print(f"📷 Image format: {prediction['mimeType']}")
            
            # Decode base64 image
            image_data = base64.b64decode(base64_image)
            print(f"📏 Decoded image size: {len(image_data)} bytes")
            
            # Save as PNG file
            output_path = os.path.join(os.getcwd(), output_filename)
            with open(output_path, 'wb') as f:
                f.write(image_data)
            
            print(f"✓ Image saved as: {output_path}")
            return output_path
            
        except Exception as e:
            print(f"✗ Error extracting and saving image: {e}")
            # Don't exit here, let the caller handle it
            raise
    
    def display_image(self, image_path: str) -> None:
        """
        Display the saved image using PIL.
        
        Args:
            image_path: Path to the image file
        """
        try:
            with Image.open(image_path) as img:
                print(f"✓ Image dimensions: {img.size}")
                print(f"✓ Image mode: {img.mode}")
                
                # Display the image
                img.show()
                print("✓ Image displayed successfully")
                
        except Exception as e:
            print(f"✗ Error displaying image: {e}")
    
    def generate_image(self, prompt: str, sample_count: int = 1, seed: int = 42, 
                      output_filename: str = "output.png") -> str:
        """
        Complete workflow to generate and save an image.
        
        Args:
            prompt: Text prompt for image generation
            sample_count: Number of images to generate
            seed: Random seed for reproducible results
            output_filename: Name of the output PNG file
            
        Returns:
            Path to the saved image file
        """
        try:
            # Load credentials and generate access token
            self.load_credentials()
            self.generate_access_token()
            
            # Create request payload
            payload = self.create_request_payload(prompt, sample_count, seed)
            
            # Send generation request
            response_data = self.send_generation_request(payload)
            
            # Extract and save image
            image_path = self.extract_and_save_image(response_data, output_filename)
            
            # Display the image
            self.display_image(image_path)
            
            return image_path
            
        except Exception as e:
            print(f"✗ Error in generate_image: {e}")
            raise


def main():
    """Main function to run the image generation script."""
    
    # Configuration
    CREDENTIALS_PATH = "key/gen-lang-client-0184415853-10648e15f9ae.json"
    
    # Extract project ID from credentials file
    try:
        with open(CREDENTIALS_PATH, 'r') as f:
            creds_data = json.load(f)
            PROJECT_ID = creds_data.get('project_id')
            if not PROJECT_ID:
                raise ValueError("No project_id found in credentials file")
    except Exception as e:
        print(f"✗ Error reading project ID from credentials: {e}")
        sys.exit(1)
    
    # Image generation prompt
    PROMPT = "a futuristic floating island with neon trees and waterfalls"
    
    # Parameters
    SAMPLE_COUNT = 1
    SEED = 42
    OUTPUT_FILENAME = "output.png"
    
    print("🚀 Starting Vertex AI Imagen Image Generation")
    print("=" * 50)
    print(f"Project ID: {PROJECT_ID}")
    print(f"Credentials: {CREDENTIALS_PATH}")
    print(f"Prompt: {PROMPT}")
    print(f"Sample Count: {SAMPLE_COUNT}")
    print(f"Seed: {SEED}")
    print("=" * 50)
    
    # Create generator instance
    generator = VertexAIImageGenerator(CREDENTIALS_PATH, PROJECT_ID)
    
    try:
        # Generate image
        image_path = generator.generate_image(
            prompt=PROMPT,
            sample_count=SAMPLE_COUNT,
            seed=SEED,
            output_filename=OUTPUT_FILENAME
        )
        
        print("=" * 50)
        print(f"🎉 Image generation completed successfully!")
        print(f"📁 Image saved to: {image_path}")
        print("=" * 50)
        
    except KeyboardInterrupt:
        print("\n⏹️  Operation cancelled by user")
        sys.exit(0)
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
