#!/bin/bash

# Docker Build Script with Retry Logic
# This script helps build Docker containers with better error handling and retry logic

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to build with retry
build_with_retry() {
    local service_name=$1
    local max_attempts=3
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        print_status "Building $service_name (attempt $attempt/$max_attempts)..."
        
        if docker-compose build --no-cache $service_name; then
            print_success "$service_name built successfully!"
            return 0
        else
            print_warning "$service_name build failed on attempt $attempt"
            if [ $attempt -lt $max_attempts ]; then
                print_status "Cleaning up and retrying in 10 seconds..."
                docker system prune -f > /dev/null 2>&1 || true
                sleep 10
            fi
            ((attempt++))
        fi
    done
    
    print_error "$service_name failed to build after $max_attempts attempts"
    return 1
}

# Function to check network connectivity
check_network() {
    print_status "Checking network connectivity..."
    
    # Test connectivity to key repositories
    local test_urls=(
        "deb.debian.org"
        "files.pythonhosted.org"
        "github.com"
        "registry.npmjs.org"
    )
    
    for url in "${test_urls[@]}"; do
        if ping -c 1 -W 5 "$url" > /dev/null 2>&1; then
            print_success "✓ $url is reachable"
        else
            print_warning "⚠ $url may have connectivity issues"
        fi
    done
}

# Main execution
main() {
    print_status "Starting Docker build process with retry logic..."
    
    # Check network first
    check_network
    
    # Clean up any existing containers/images
    print_status "Cleaning up existing containers and images..."
    docker-compose down --volumes --remove-orphans > /dev/null 2>&1 || true
    docker system prune -f > /dev/null 2>&1 || true
    
    # Build services
    local services=("video-service")
    local failed_services=()
    
    for service in "${services[@]}"; do
        if ! build_with_retry "$service"; then
            failed_services+=("$service")
        fi
    done
    
    # Report results
    if [ ${#failed_services[@]} -eq 0 ]; then
        print_success "All services built successfully!"
        print_status "Starting services..."
        docker-compose up -d
        print_success "All services are now running!"
    else
        print_error "The following services failed to build:"
        for service in "${failed_services[@]}"; do
            echo "  - $service"
        done
        print_status "You may need to check your network connection or try again later."
        exit 1
    fi
}

# Handle script arguments
case "${1:-}" in
    "video-only")
        print_status "Building video service only..."
        build_with_retry "video-service"
        ;;
    "check-network")
        check_network
        ;;
    *)
        main
        ;;
esac
