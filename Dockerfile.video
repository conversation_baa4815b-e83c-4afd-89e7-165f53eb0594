# Dockerfile for Enhanced Video Generation Service (9:16 + ASS Subtitles)
FROM node:20-bullseye

# Install enhanced system dependencies for video generation with subtitle support
RUN apt-get update && \
    apt-get install -y --fix-missing --no-install-recommends \
    ffmpeg \
    fonts-noto \
    fonts-noto-extra \
    fonts-indic \
    fontconfig \
    libass9 \
    libfontconfig1 \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Update font cache for better Telugu font support
RUN fc-cache -fv

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install Node.js dependencies
RUN npm install

# Copy application files
COPY . .

# Create a non-root user for security
RUN groupadd -r videouser && useradd -r -g videouser videouser
RUN chown -R videouser:videouser /app

# Switch to non-root user
USER videouser

# Expose port
EXPOSE 3002

# Start the enhanced application
CMD ["node", "enhanced-video-service.js"]