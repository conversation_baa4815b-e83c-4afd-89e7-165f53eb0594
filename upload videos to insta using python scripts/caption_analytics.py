import json
import os
import re
from datetime import datetime
from typing import Dict, List, Any, Optional
from logger import log_info, log_error, log_warning

class CaptionAnalyzer:
    """Advanced caption analytics for Instagram Bhagavad Gita content."""
    
    def __init__(self, data_file='analytics_data.json'):
        self.data_file = data_file
        self.data = self._load_data()
        
        # Telugu spiritual keywords for engagement scoring
        self.telugu_spiritual_keywords = [
            'కర్మ', 'ధర్మ', 'యోగ', 'భక్తి', 'మోక్ష', 'ఆత్మ', 'పరమాత్మ', 'గీత',
            'కృష్ణ', 'అర్జున', 'వేద', 'ఉపనిషత్', 'స్వధర్మ', 'సంన్యాస', 'ధ్యాన'
        ]
        
        # English spiritual keywords
        self.english_spiritual_keywords = [
            'karma', 'dharma', 'yoga', 'devotion', 'soul', 'krishna', 'arjuna',
            'meditation', 'consciousness', 'surrender', 'detachment', 'duty',
            'wisdom', 'spiritual', 'divine', 'eternal', 'truth', 'peace'
        ]
        
    def _load_data(self) -> Dict:
        """Load analytics data from file."""
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                log_error(f"Error loading analytics data: {e}")
        return {'captions': [], 'performance': {}}
    
    def _save_data(self):
        """Save analytics data to file."""
        try:
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(self.data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            log_error(f"Error saving analytics data: {e}")
    
    def analyze_caption(self, caption: str, metadata: Dict = None) -> Dict[str, Any]:
        """Comprehensive caption analysis."""
        try:
            analysis_id = f"analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # Basic metrics
            length_analysis = self._analyze_length(caption)
            
            # Content analysis
            keyword_analysis = self._analyze_keywords(caption)
            structure_analysis = self._analyze_structure(caption)
            readability_analysis = self._analyze_readability(caption)
            
            # Performance prediction
            performance_score = self._calculate_performance_score(
                length_analysis, keyword_analysis, structure_analysis, readability_analysis
            )
            
            # Engagement prediction
            engagement_prediction = self._predict_engagement(performance_score, keyword_analysis)
            
            analysis = {
                'id': analysis_id,
                'timestamp': datetime.now().isoformat(),
                'caption': caption,
                'metadata': metadata or {},
                'length_analysis': length_analysis,
                'keyword_analysis': keyword_analysis,
                'structure_analysis': structure_analysis,
                'readability_analysis': readability_analysis,
                'performance_score': performance_score,
                'engagement_prediction': engagement_prediction
            }
            
            # Store analysis
            self.data['captions'].append(analysis)
            self._save_data()
            
            log_info(f"Caption analysis completed: {analysis_id}")
            return analysis
            
        except Exception as e:
            log_error(f"Error analyzing caption: {e}")
            return {}
    
    def _analyze_length(self, caption: str) -> Dict[str, Any]:
        """Analyze caption length metrics."""
        lines = caption.split('\n')
        words = caption.split()
        
        return {
            'character_count': len(caption),
            'word_count': len(words),
            'line_count': len(lines),
            'avg_words_per_line': len(words) / len(lines) if lines else 0,
            'is_optimal_length': 800 <= len(caption) <= 2200,  # Instagram optimal range
            'hook_length': len(lines[0]) if lines else 0
        }
    
    def _analyze_keywords(self, caption: str) -> Dict[str, Any]:
        """Analyze spiritual keyword usage."""
        caption_lower = caption.lower()
        
        # Count Telugu keywords
        telugu_count = sum(1 for keyword in self.telugu_spiritual_keywords 
                          if keyword in caption)
        
        # Count English keywords
        english_count = sum(1 for keyword in self.english_spiritual_keywords 
                           if keyword in caption_lower)
        
        # Find hashtags
        hashtags = re.findall(r'#\w+', caption)
        
        # Analyze hashtag relevance and diversity
        hashtag_relevance = self._analyze_hashtag_relevance(hashtags, caption)
        
        return {
            'telugu_spiritual_keywords': telugu_count,
            'english_spiritual_keywords': english_count,
            'total_spiritual_keywords': telugu_count + english_count,
            'hashtag_count': len(hashtags),
            'hashtags': hashtags,
            'hashtag_relevance_score': hashtag_relevance['relevance_score'],
            'hashtag_diversity_score': hashtag_relevance['diversity_score'],
            'generic_hashtag_penalty': hashtag_relevance['generic_penalty'],
            'has_sanskrit': bool(re.search(r'[ऀ-ॿ]', caption)),  # Devanagari script
            'keyword_density': (telugu_count + english_count) / len(caption.split()) if caption.split() else 0
        }
    
    def _analyze_hashtag_relevance(self, hashtags: List[str], caption: str) -> Dict[str, Any]:
        """Analyze hashtag relevance and diversity for verse-specific content."""
        try:
            # Define generic hashtags that should be penalized
            generic_hashtags = {
                '#karma', '#dharma', '#innerpeace', '#devotion', 
                '#spiritualgrowth', '#consciousness', '#wisdom'
            }
            
            # Define verse-specific hashtag categories
            verse_themes = {
                'duty': ['#duty', '#righteousaction', '#karmayoga', '#selflessservice'],
                'devotion': ['#bhakti', '#devotion', '#surrender', '#divinelove'],
                'knowledge': ['#wisdom', '#selfknowledge', '#spiritualwisdom', '#enlightenment'],
                'detachment': ['#detachment', '#innerpeace', '#equanimity', '#mindfulness'],
                'grief': ['#grief', '#sorrow', '#mentalpeace', '#emotionalhealing'],
                'meditation': ['#meditation', '#concentration', '#mindcontrol', '#spiritualpractice'],
                'dharma': ['#dharma', '#righteousness', '#moralduty', '#ethics'],
                'soul': ['#soul', '#atman', '#consciousness', '#selfRealization']
            }
            
            # Calculate relevance score
            relevance_score = 0
            diversity_score = 0
            generic_count = 0
            
            # Check if hashtags match caption content themes
            caption_lower = caption.lower()
            
            for hashtag in hashtags:
                hashtag_lower = hashtag.lower()
                
                # Check if it's a generic hashtag
                if hashtag_lower in generic_hashtags:
                    generic_count += 1
                
                # Check theme relevance
                theme_matched = False
                for theme, theme_hashtags in verse_themes.items():
                    if hashtag_lower in theme_hashtags:
                        # Check if the caption contains theme-related content
                        theme_keywords = {
                            'duty': ['duty', 'action', 'karma', 'work', 'dharma'],
                            'devotion': ['devotion', 'love', 'surrender', 'bhakti'],
                            'knowledge': ['knowledge', 'wisdom', 'understand', 'learn'],
                            'detachment': ['detachment', 'peace', 'calm', 'equanimity'],
                            'grief': ['grief', 'sorrow', 'sad', 'pain', 'suffering'],
                            'meditation': ['meditation', 'mind', 'concentrate', 'focus'],
                            'dharma': ['dharma', 'righteous', 'moral', 'ethics'],
                            'soul': ['soul', 'atman', 'self', 'consciousness']
                        }
                        
                        if any(keyword in caption_lower for keyword in theme_keywords.get(theme, [])):
                            relevance_score += 10
                            theme_matched = True
                            break
                
                if not theme_matched:
                    relevance_score += 2  # Base score for any spiritual hashtag
            
            # Calculate diversity (unique themes covered)
            themes_covered = set()
            for hashtag in hashtags:
                hashtag_lower = hashtag.lower()
                for theme, theme_hashtags in verse_themes.items():
                    if hashtag_lower in theme_hashtags:
                        themes_covered.add(theme)
                        break
            
            diversity_score = len(themes_covered) * 10
            
            # Penalty for too many generic hashtags
            generic_penalty = generic_count * 5 if generic_count > 2 else 0
            
            return {
                'relevance_score': min(100, relevance_score),
                'diversity_score': min(100, diversity_score),
                'generic_penalty': generic_penalty,
                'themes_covered': list(themes_covered),
                'generic_count': generic_count
            }
            
        except Exception as e:
            log_error(f"Error analyzing hashtag relevance: {e}")
            return {'relevance_score': 50, 'diversity_score': 50, 'generic_penalty': 0}
    
    def _analyze_structure(self, caption: str) -> Dict[str, Any]:
        """Analyze caption structure."""
        lines = [line.strip() for line in caption.split('\n') if line.strip()]
        
        # Check for required elements
        has_title = bool(re.search(r'భగవద్గీత \d+\.\d+', caption))
        has_chapter_verse = bool(re.search(r'Chapter-Verse: \d+-\d+', caption))
        has_sanskrit = bool(re.search(r'[ऀ-ॿ]', caption))
        has_translation = 'English Translation:' in caption or 'Translation:' in caption
        has_word_meaning = 'Word-by-Word' in caption or 'word meaning' in caption.lower()
        has_discovery_text = 'Discover more' in caption
        
        return {
            'has_title': has_title,
            'has_chapter_verse': has_chapter_verse,
            'has_sanskrit': has_sanskrit,
            'has_translation': has_translation,
            'has_word_meaning': has_word_meaning,
            'has_discovery_text': has_discovery_text,
            'structure_completeness': sum([
                has_title, has_chapter_verse, has_sanskrit, 
                has_translation, has_word_meaning, has_discovery_text
            ]) / 6,
            'paragraph_count': len([line for line in lines if len(line) > 50])
        }
    
    def _analyze_readability(self, caption: str) -> Dict[str, Any]:
        """Analyze caption readability."""
        words = caption.split()
        sentences = re.split(r'[.!?]+', caption)
        sentences = [s.strip() for s in sentences if s.strip()]
        
        # Simple readability metrics
        avg_word_length = sum(len(word) for word in words) / len(words) if words else 0
        avg_sentence_length = len(words) / len(sentences) if sentences else 0
        
        # Emoji analysis
        emoji_pattern = re.compile(r'[\U0001F600-\U0001F64F\U0001F300-\U0001F5FF\U0001F680-\U0001F6FF\U0001F1E0-\U0001F1FF\U00002702-\U000027B0\U000024C2-\U0001F251]+')
        emoji_count = len(emoji_pattern.findall(caption))
        
        return {
            'avg_word_length': avg_word_length,
            'avg_sentence_length': avg_sentence_length,
            'emoji_count': emoji_count,
            'has_emojis': emoji_count > 0,
            'readability_score': max(0, min(100, 100 - (avg_sentence_length * 2)))  # Simple score
        }
    
    def _calculate_performance_score(self, length_analysis: Dict, keyword_analysis: Dict, 
                                   structure_analysis: Dict, readability_analysis: Dict) -> float:
        """Calculate overall performance score (0-100)."""
        try:
            score = 0
            
            # Length score (20 points)
            if length_analysis['is_optimal_length']:
                score += 20
            elif length_analysis['character_count'] > 500:
                score += 15
            else:
                score += 10
            
            # Keyword score (25 points)
            keyword_score = min(25, keyword_analysis['total_spiritual_keywords'] * 3)
            score += keyword_score
            
            # Structure score (30 points)
            structure_score = structure_analysis['structure_completeness'] * 30
            score += structure_score
            
            # Readability score (10 points)
            readability_score = (readability_analysis['readability_score'] / 100) * 10
            score += readability_score
            
            # Enhanced hashtag scoring (15 points total)
            # Base hashtag count (5 points)
            hashtag_count_score = min(5, keyword_analysis['hashtag_count'] * 1)
            score += hashtag_count_score
            
            # Hashtag relevance (5 points)
            hashtag_relevance_score = (keyword_analysis['hashtag_relevance_score'] / 100) * 5
            score += hashtag_relevance_score
            
            # Hashtag diversity bonus (5 points)
            hashtag_diversity_score = (keyword_analysis['hashtag_diversity_score'] / 100) * 5
            score += hashtag_diversity_score
            
            # Generic hashtag penalty
            score -= keyword_analysis['generic_hashtag_penalty']
            
            return min(100, max(0, score))
            
        except Exception as e:
            log_error(f"Error calculating performance score: {e}")
            return 0
    
    def _predict_engagement(self, performance_score: float, keyword_analysis: Dict) -> str:
        """Predict engagement level based on analysis."""
        try:
            # Adjust score based on spiritual content
            adjusted_score = performance_score
            
            if keyword_analysis['telugu_spiritual_keywords'] >= 3:
                adjusted_score += 10
            
            if keyword_analysis['has_sanskrit']:
                adjusted_score += 5
            
            if keyword_analysis['hashtag_count'] >= 5:
                adjusted_score += 5
            
            if adjusted_score >= 85:
                return "Very High"
            elif adjusted_score >= 70:
                return "High"
            elif adjusted_score >= 55:
                return "Medium"
            elif adjusted_score >= 40:
                return "Low"
            else:
                return "Very Low"
                
        except Exception as e:
            log_error(f"Error predicting engagement: {e}")
            return "Unknown"
    
    def log_upload_result(self, analysis_id: str, success: bool, result_data: Any = None):
        """Log the upload result for an analysis."""
        try:
            for caption_data in self.data['captions']:
                if caption_data['id'] == analysis_id:
                    caption_data['upload_result'] = {
                        'success': success,
                        'timestamp': datetime.now().isoformat(),
                        'result_data': result_data
                    }
                    break
            
            self._save_data()
            log_info(f"Upload result logged for analysis: {analysis_id}")
            
        except Exception as e:
            log_error(f"Error logging upload result: {e}")
    
    def generate_performance_report(self) -> Dict[str, Any]:
        """Generate comprehensive performance report."""
        try:
            if not self.data['captions']:
                return {}
            
            captions = self.data['captions']
            
            # Calculate averages
            avg_performance_score = sum(c['performance_score'] for c in captions) / len(captions)
            avg_length = sum(c['length_analysis']['character_count'] for c in captions) / len(captions)
            avg_keywords = sum(c['keyword_analysis']['total_spiritual_keywords'] for c in captions) / len(captions)
            
            # Upload success rate
            uploads = [c for c in captions if 'upload_result' in c]
            success_rate = (sum(1 for u in uploads if u['upload_result']['success']) / len(uploads) * 100) if uploads else 0
            
            # Top performing captions
            top_performers = sorted(captions, key=lambda x: x['performance_score'], reverse=True)[:5]
            
            report = {
                'total_captions': len(captions),
                'avg_performance_score': avg_performance_score,
                'avg_character_length': avg_length,
                'avg_spiritual_keywords': avg_keywords,
                'upload_success_rate': success_rate,
                'top_performers': [
                    {
                        'id': tp['id'],
                        'chapter_verse': f"{tp['metadata'].get('chapter', '?')}.{tp['metadata'].get('verse', '?')}",
                        'score': tp['performance_score'],
                        'engagement': tp['engagement_prediction']
                    }
                    for tp in top_performers
                ]
            }
            
            log_info("Performance report generated successfully")
            return report
            
        except Exception as e:
            log_error(f"Error generating performance report: {e}")
            return {}
    
    def get_optimization_suggestions(self, caption: str) -> List[str]:
        """Get suggestions to optimize caption performance."""
        suggestions = []
        analysis = self.analyze_caption(caption)
        
        try:
            # Length suggestions
            length = analysis['length_analysis']['character_count']
            if length < 800:
                suggestions.append("Consider adding more detail - captions under 800 characters may not provide enough value")
            elif length > 2200:
                suggestions.append("Consider shortening - very long captions may lose reader attention")
            
            # Keyword suggestions
            if analysis['keyword_analysis']['total_spiritual_keywords'] < 3:
                suggestions.append("Add more spiritual keywords (karma, dharma, devotion, etc.) to improve discoverability")
            
            # Structure suggestions
            structure = analysis['structure_analysis']
            if not structure['has_title']:
                suggestions.append("Add a Telugu title with chapter.verse format")
            if not structure['has_sanskrit']:
                suggestions.append("Include the original Sanskrit verse")
            if not structure['has_translation']:
                suggestions.append("Add English translation section")
            
            # Hashtag suggestions
            if analysis['keyword_analysis']['hashtag_count'] < 5:
                suggestions.append("Add more relevant hashtags (aim for 5-7)")
            
            return suggestions
            
        except Exception as e:
            log_error(f"Error generating optimization suggestions: {e}")
            return []
