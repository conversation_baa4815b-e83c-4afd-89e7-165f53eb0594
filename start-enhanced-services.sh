#!/bin/bash

# Enhanced Docker Startup Script for Bhagavad Gita Video Generation
# This script builds and starts all services with multi-image support

echo "🚀 Starting Enhanced Bhagavad Gita Video Generation Services..."
echo ""

# Function to check if Dock<PERSON> is running
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        echo "❌ Docker is not running. Please start Docker Desktop and try again."
        exit 1
    fi
    echo "✅ Docker is running"
}

# Function to build services
build_services() {
    echo "🔨 Building Docker services..."
    
    # Build enhanced video service with multi-image support
    echo "🎬 Building Enhanced Video service (with multi-image support)..."
    docker build -f Dockerfile.video -t enhanced-video-service .
    
    echo "✅ Services built successfully"
}

# Function to start services
start_services() {
    echo "🚀 Starting all services..."
    
    # Start services with docker-compose
    docker-compose up -d
    
    echo "✅ All services started"
}

# Function to show service status
show_status() {
    echo ""
    echo "📊 Service Status:"
    docker-compose ps
    
    echo ""
    echo "🌐 Service URLs:"
    echo "  • TTS Service: http://localhost:3001/health"
    echo "  • Enhanced Video Service: http://localhost:3002/health"
    echo "  • N8N Workflow: http://localhost:5678"
    echo ""
    echo "🎯 Enhanced Features Available:"
    echo "  • Multi-Image Support (a,b,c,d,e variants)"
    echo "  • Fade Transitions between images"
    echo "  • ASS Subtitle Integration"
    echo "  • 9:16 Vertical Video Format"
    echo ""
}

# Function to show logs
show_logs() {
    echo "📋 Service Logs (last 20 lines):"
    echo ""
    echo "--- Enhanced Video Service ---"
    docker-compose logs --tail=20 video-service
    echo ""
    echo "--- Video Service ---"
    docker-compose logs --tail=20 video-service
}

# Main execution
main() {
    echo "🎬 Enhanced Bhagavad Gita Video Generation Docker Setup"
    echo "=================================================="
    
    check_docker
    
    # Parse command line arguments
    case "${1:-start}" in
        "build")
            build_services
            ;;
        "start")
            build_services
            start_services
            show_status
            ;;
        "status")
            show_status
            ;;
        "logs")
            show_logs
            ;;
        "stop")
            echo "🛑 Stopping all services..."
            docker-compose down
            echo "✅ All services stopped"
            ;;
        "restart")
            echo "🔄 Restarting all services..."
            docker-compose down
            build_services
            start_services
            show_status
            ;;
        *)
            echo "Usage: $0 {build|start|status|logs|stop|restart}"
            echo ""
            echo "Commands:"
            echo "  build   - Build Docker images"
            echo "  start   - Build and start all services (default)"
            echo "  status  - Show service status"
            echo "  logs    - Show service logs"
            echo "  stop    - Stop all services"
            echo "  restart - Restart all services"
            exit 1
            ;;
    esac
}

# Run the main function with all arguments
main "$@"
