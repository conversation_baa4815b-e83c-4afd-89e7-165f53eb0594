# Docker Compose file for Simplified Bhagavad Gita N8N Video Generation Workflow (No Subtitles)

services:
  video-service:
    build:
      context: .
      dockerfile: Dockerfile.video
    container_name: enhanced-video-service
    ports:
      - "3002:3002"
    volumes:
      - ./shared-data/downloads:/app/shared-data/downloads
      - ./shared-data/videos:/app/shared-data/videos
      - ./shared-data/images:/app/shared-data/images
      - ./shared-data/temp:/app/shared-data/temp
      - ./shared-data/ass-scripts:/app/shared-data/ass-scripts
      # Mount media folder for FFmpeg integration
      - ./shared-data/media:/app/shared-data/media
      # Mount full shared-data for access to all folders
      - ./shared-data:/app/shared-data
    environment:
      - PORT=3002
      - VIDEO_FORMAT=9:16
      - DEFAULT_WIDTH=1080
      - DEFAULT_HEIGHT=1920
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '3.0'
        reservations:
          memory: 1G
          cpus: '1.0'
    networks:
      - tts-network
    healthcheck:
      test: ["C<PERSON>", "curl", "-f", "http://localhost:3002/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  n8n:
    image: n8nio/n8n:1.103.2
    container_name: bhagavad-gita-n8n
    ports:
      - "5678:5678"
    environment:
      # Basic Authentication
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD=admin123

      # Timezone
      - GENERIC_TIMEZONE=UTC

      # Security
      - N8N_SECURE_COOKIE=false
      - N8N_ENFORCE_SETTINGS_FILE_PERMISSIONS=true

      # Webhook configuration
      - WEBHOOK_URL=http://localhost:5678/

      # File paths
      - N8N_USER_FOLDER=/home/<USER>/.n8n

      # Database (using SQLite for simplicity)
      - DB_TYPE=sqlite
      - DB_SQLITE_DATABASE=/home/<USER>/.n8n/database.sqlite

      # Execution data optimization (prevent binary data storage)
      - EXECUTIONS_DATA_SAVE_ON_ERROR=none
      - EXECUTIONS_DATA_SAVE_ON_SUCCESS=none
      - EXECUTIONS_DATA_SAVE_MANUAL_EXECUTIONS=false
      - EXECUTIONS_DATA_MAX_AGE=168
      - EXECUTIONS_DATA_PRUNE=true

      # Execution settings
      - N8N_RUNNERS_ENABLED=true
      - EXECUTIONS_TIMEOUT=3600
      - EXECUTIONS_TIMEOUT_MAX=7200

      # Logging
      - N8N_LOG_LEVEL=info

      # Node environment
      - NODE_ENV=production

      # Service endpoints - now using integrated video service
      - VIDEO_SERVICE_HOST=enhanced-video-service
      - VIDEO_SERVICE_URL=http://enhanced-video-service:3002

    volumes:
      # Persist N8N data in local directory (survives rebuilds)
      - ./n8n-data:/home/<USER>/.n8n

      # Mount Docker socket for container communication
      - /var/run/docker.sock:/var/run/docker.sock

      # Shared data folders (same as other services)
      - ./shared-data/downloads:/shared-data/downloads
      - ./shared-data/videos:/shared-data/videos
      - ./shared-data/images:/shared-data/images
      - ./shared-data/temp:/shared-data/temp
      - ./shared-data/ass-scripts:/shared-data/ass-scripts
      - ./shared-data/media:/shared-data/media

      # Mount workflow files directory (n8n will create structure)
      - ./workflows:/home/<USER>/.n8n/workflows

    networks:
      - tts-network

    restart: unless-stopped

    depends_on:
      - video-service

    # Health check
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:5678/healthz || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  tts-network:
    driver: bridge


