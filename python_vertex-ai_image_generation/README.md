# Vertex AI Imagen Image Generator

This Python script generates images using Google Cloud's Vertex AI Imagen model. It loads service account credentials, generates OAuth 2.0 access tokens, and makes API calls to create images from text prompts.

## Features

- ✅ Load Google Cloud service account credentials from JSON file
- ✅ Generate OAuth 2.0 access tokens with proper scopes
- ✅ Send POST requests to Vertex AI Imagen endpoint
- ✅ Extract and save base64 image responses as PNG files
- ✅ Display generated images using Pillow
- ✅ Comprehensive error handling and logging
- ✅ Configurable parameters (prompt, sample count, seed)

## Prerequisites

1. **Google Cloud Project**: You need a Google Cloud project with Vertex AI API enabled
2. **Service Account**: Create a service account with appropriate permissions:
   - Vertex AI User role
   - AI Platform Admin role (optional, for broader access)
3. **Service Account Key**: Download the JSON key file for your service account

## Installation

1. Install the required dependencies:
```bash
pip install -r requirements.txt
```

2. Ensure your service account JSON file is in the `key/` directory

## Usage

Run the script:
```bash
python vertex_ai_image_generator.py
```

The script will:
1. Load credentials from `key/gen-lang-client-**********-10648e15f9ae.json`
2. Generate an image with the prompt: "a futuristic floating island with neon trees and waterfalls"
3. Save the image as `output.png`
4. Display the image using your default image viewer

## Configuration

You can modify the following variables in the `main()` function:

- `PROMPT`: The text prompt for image generation
- `SAMPLE_COUNT`: Number of images to generate (default: 1)
- `SEED`: Random seed for reproducible results (default: 42)
- `OUTPUT_FILENAME`: Name of the output PNG file (default: "output.png")

## API Details

The script uses the Vertex AI Imagen 4.0 model endpoint:
```
https://us-central1-aiplatform.googleapis.com/v1/projects/<PROJECT_ID>/locations/us-central1/publishers/google/models/imagen-4.0-generate-preview-06-06:predict
```

## Error Handling

The script includes comprehensive error handling for:
- Missing or invalid credentials files
- Authentication failures
- API request errors
- Image processing errors
- Network timeouts

## Dependencies

- `google-auth`: For OAuth 2.0 authentication
- `requests`: For HTTP API calls
- `Pillow`: For image processing and display
- `json`, `base64`, `os`, `sys`: Built-in Python modules

## Troubleshooting

1. **Authentication Error**: Ensure your service account has the correct permissions
2. **API Error**: Check that Vertex AI API is enabled in your Google Cloud project
3. **Project ID Error**: Verify the project_id in your credentials JSON file
4. **Image Display Issues**: Make sure you have a default image viewer configured

## License

This project is for educational and development purposes.
