#!/usr/bin/env python3

import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any
from caption_analytics import CaptionAnalyzer
from logger import log_info, log_error

class AnalyticsDashboard:
    """Interactive analytics dashboard for Instagram caption performance."""
    
    def __init__(self):
        self.analyzer = CaptionAnalyzer()
        
    def display_main_menu(self):
        """Display main dashboard menu."""
        print("\n" + "="*60)
        print("🚀 INSTAGRAM BHAGAVAD GITA ANALYTICS DASHBOARD")
        print("="*60)
        print("1. Performance Overview")
        print("2. Detailed Caption Analysis")
        print("3. Top Performing Captions")
        print("4. Upload Success Metrics")
        print("5. Keyword Analysis")
        print("6. Generate Performance Report")
        print("7. Export Analytics Data")
        print("8. Optimization Suggestions")
        print("0. Exit")
        print("="*60)
        
    def run_dashboard(self):
        """Run the interactive dashboard."""
        try:
            while True:
                self.display_main_menu()
                choice = input("\nEnter your choice (0-8): ").strip()
                
                if choice == '0':
                    print("Thank you for using the Analytics Dashboard! 🙏")
                    break
                elif choice == '1':
                    self.show_performance_overview()
                elif choice == '2':
                    self.show_detailed_analysis()
                elif choice == '3':
                    self.show_top_performers()
                elif choice == '4':
                    self.show_upload_metrics()
                elif choice == '5':
                    self.show_keyword_analysis()
                elif choice == '6':
                    self.generate_report()
                elif choice == '7':
                    self.export_data()
                elif choice == '8':
                    self.show_optimization_suggestions()
                else:
                    print("Invalid choice. Please try again.")
                
                input("\nPress Enter to continue...")
                
        except KeyboardInterrupt:
            print("\n\nDashboard interrupted by user. Goodbye! 🙏")
        except Exception as e:
            log_error(f"Error in dashboard: {e}")
            print(f"An error occurred: {e}")
    
    def show_performance_overview(self):
        """Show overall performance metrics."""
        print("\n" + "="*50)
        print("📊 PERFORMANCE OVERVIEW")
        print("="*50)
        
        try:
            data = self.analyzer.data
            if not data['captions']:
                print("No caption data available yet.")
                return
            
            captions = data['captions']
            
            # Basic metrics
            total_captions = len(captions)
            avg_score = sum(c['performance_score'] for c in captions) / total_captions
            avg_length = sum(c['length_analysis']['character_count'] for c in captions) / total_captions
            
            # Upload metrics
            uploads = [c for c in captions if 'upload_result' in c]
            success_rate = (sum(1 for u in uploads if u['upload_result']['success']) / len(uploads) * 100) if uploads else 0
            
            # Date range
            dates = [datetime.fromisoformat(c['timestamp']) for c in captions]
            date_range = f"{min(dates).strftime('%Y-%m-%d')} to {max(dates).strftime('%Y-%m-%d')}"
            
            print(f"📈 Total Captions Analyzed: {total_captions}")
            print(f"⭐ Average Performance Score: {avg_score:.1f}/100")
            print(f"📝 Average Caption Length: {avg_length:.0f} characters")
            print(f"🎯 Upload Success Rate: {success_rate:.1f}%")
            print(f"📅 Date Range: {date_range}")
            
            # Performance distribution
            high_performers = sum(1 for c in captions if c['performance_score'] >= 70)
            medium_performers = sum(1 for c in captions if 50 <= c['performance_score'] < 70)
            low_performers = sum(1 for c in captions if c['performance_score'] < 50)
            
            print(f"\n🏆 Performance Distribution:")
            print(f"   High (70-100): {high_performers} ({high_performers/total_captions*100:.1f}%)")
            print(f"   Medium (50-69): {medium_performers} ({medium_performers/total_captions*100:.1f}%)")
            print(f"   Low (0-49): {low_performers} ({low_performers/total_captions*100:.1f}%)")
            
        except Exception as e:
            log_error(f"Error showing performance overview: {e}")
            print("Error generating performance overview.")

def main():
    """Run the analytics dashboard."""
    dashboard = AnalyticsDashboard()
    dashboard.run_dashboard()

if __name__ == "__main__":
    main()
