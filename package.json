{"name": "bhagavad-gita-video-automation", "version": "1.0.0", "description": "Simplified video generation using n8n workflow", "main": "enhanced-video-service.js", "scripts": {"start": "node enhanced-video-service.js", "start:video": "node enhanced-video-service.js", "dev": "nodemon enhanced-video-service.js", "dev:video": "nodemon enhanced-video-service.js", "health-check": "curl -s http://localhost:3002/health"}, "keywords": ["automation", "video-generation", "telugu", "n8n", "bhagavad-gita", "ffmpeg"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "multer": "^1.4.5-lts.1", "fluent-ffmpeg": "^2.1.2"}, "engines": {"node": ">=16.0.0"}}