#!/bin/bash

# Maintenance and Health Check Script for Bhagavad Gita Video Generation
# This script provides maintenance utilities and health checks

set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Health check function
health_check() {
    print_status "🏥 Performing comprehensive health check..."
    echo ""
    
    # Check Docker containers
    print_status "📦 Container Status:"
    if docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "(enhanced-video-service|bhagavad-gita-n8n)"; then
        print_success "✅ All main containers are running"
    else
        print_error "❌ Some containers are not running"
        return 1
    fi
    
    echo ""
    
    # Check services
    print_status "🌐 Service Health Checks:"
    
    # N8N Health
    if curl -s http://localhost:5678/healthz > /dev/null; then
        print_success "✅ N8N: Healthy"
    else
        print_warning "⚠️  N8N: Not responding"
    fi
    
    # TTS Service Health
    if curl -s http://localhost:3001/health > /dev/null; then
        print_success "✅ TTS Service: Healthy"
    else
        print_warning "⚠️  TTS Service: Not responding"
    fi
    
    # Video Service Health
    if curl -s http://localhost:3002/health > /dev/null; then
        print_success "✅ Video Service: Healthy"
    else
        print_warning "⚠️  Video Service: Not responding"
    fi
    
    echo ""
    
    # Check shared data structure
    print_status "📁 Shared Data Structure:"
    for dir in downloads videos images temp ass-scripts; do
        if [ -d "shared-data/$dir" ]; then
            file_count=$(find "shared-data/$dir" -type f | wc -l | tr -d ' ')
            print_success "✅ shared-data/$dir/ exists ($file_count files)"
        else
            print_error "❌ shared-data/$dir/ missing"
        fi
    done
    
    echo ""
    print_success "🎉 Health check completed!"
}

# File sharing test
test_file_sharing() {
    print_status "🔗 Testing file sharing between containers..."
    
    # Create test file in N8N
    test_file="maintenance-test-$(date +%s).txt"
    if docker exec bhagavad-gita-n8n touch "/shared-data/temp/$test_file" 2>/dev/null; then
        print_success "✅ File created in N8N container"
        
        # Check if video service can see it
        if docker exec enhanced-video-service ls "/app/temp/$test_file" > /dev/null 2>&1; then
            print_success "✅ File visible in Video Service container"
            
            # Clean up
            docker exec enhanced-video-service rm "/app/temp/$test_file" 2>/dev/null || true
            print_success "✅ File sharing test passed"
        else
            print_error "❌ File not visible in Video Service container"
        fi
    else
        print_error "❌ Could not create test file in N8N container"
    fi
}

# Directory cleanup
cleanup_temp_files() {
    print_status "🧹 Cleaning up temporary files..."
    
    # Remove test files and temporary files older than 1 hour
    find shared-data/temp/ -type f -name "*test*" -delete 2>/dev/null || true
    find shared-data/temp/ -type f -mmin +60 -delete 2>/dev/null || true
    
    print_success "✅ Temporary files cleaned"
}

# Show usage statistics
show_stats() {
    print_status "📊 Usage Statistics:"
    echo ""
    
    echo "📁 File Counts:"
    for dir in downloads videos images temp ass-scripts; do
        if [ -d "shared-data/$dir" ]; then
            file_count=$(find "shared-data/$dir" -type f | wc -l | tr -d ' ')
            size=$(du -sh "shared-data/$dir" 2>/dev/null | cut -f1 || echo "0B")
            echo "   • $dir/: $file_count files ($size)"
        fi
    done
    
    echo ""
    echo "🐳 Container Resources:"
    docker stats --no-stream --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}" | grep -E "(enhanced-video-service|bhagavad-gita-n8n)" || echo "   No containers running"
}

# Main function
main() {
    echo "🔧 Bhagavad Gita Video Generation - Maintenance Tool"
    echo "=================================================="
    echo ""
    
    case "${1:-help}" in
        "health"|"check")
            health_check
            ;;
        "test")
            test_file_sharing
            ;;
        "cleanup")
            cleanup_temp_files
            ;;
        "stats")
            show_stats
            ;;
        "full")
            health_check
            echo ""
            test_file_sharing
            echo ""
            cleanup_temp_files
            echo ""
            show_stats
            ;;
        "help"|*)
            echo "Usage: $0 {health|test|cleanup|stats|full}"
            echo ""
            echo "Commands:"
            echo "  health   - Check service health and data structure"
            echo "  test     - Test file sharing between containers"
            echo "  cleanup  - Clean up temporary files"
            echo "  stats    - Show usage statistics"
            echo "  full     - Run all checks and maintenance"
            echo ""
            echo "Service URLs:"
            echo "  • N8N: http://localhost:5678"
            echo "  • TTS Service: http://localhost:3001"
            echo "  • Video Service: http://localhost:3002"
            ;;
    esac
}

main "$@"
