{"nodes": [{"parameters": {"content": "```\n╔════════════════════════════════════════════════════╗\n║                                                    ║\n║   🔵 ONLY GOOGLE-DEPENDENT $300 MODEL USAGE         ║\n║         (Google Cloud Credit Usage Limit)          ║\n║                                                    ║\n╚════════════════════════════════════════════════════╝\n```\n", "width": 640}, "type": "n8n-nodes-base.stickyNote", "position": [256, 1376], "typeVersion": 1, "id": "a8f1f6aa-556d-4e51-942d-3da09ebf0cb9", "name": "Sticky Note5"}, {"parameters": {"jsCode": "return [{\n  binary: {\n    data: {\n      data: $json[\"candidates\"][0].content.parts[0].inlineData.data,\n      mimeType: 'audio/L16',\n      fileName: 'out.pcm',\n      fileExtension: 'pcm'\n    }\n  }\n}];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [864, 1936], "id": "6c6829ea-4d23-4fc5-8a7c-ed67ae963908", "name": "Code9", "alwaysOutputData": true}, {"parameters": {"method": "POST", "url": "http://enhanced-video-service:3002/convert-pcm-to-mp3", "sendQuery": true, "queryParameters": {"parameters": [{"name": "output_name", "value": "=Chapter_{{ $('Get row(s) in sheet').item.json[\"Chapter-Verse\"].split('-')[0] }}_Verse_{{ $('Get row(s) in sheet').item.json[\"Chapter-Verse\"].split('-')[1] }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1344, 1936], "id": "8cb15ab6-c327-4079-bcb0-8b78bcf8ad3e", "name": "HTTP Request30"}, {"parameters": {"operation": "write", "fileName": "/shared-data/downloads/out.pcm", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [1072, 1936], "id": "55f0da21-cbb6-45af-8ab1-21027784e454", "name": "Read/Write Files from Disk19"}, {"parameters": {"promptType": "define", "text": "=Please create a Telugu voiceover script based on the following information:\n\nChapter-Verse: {{ $json['Chapter-Verse'] }}\nSanskrit Sloka: {{ $json['Sanskrit Shloka'] }}\nRomanized Transliteration: {{ $json['Romanized Transliteration'] }}\nEnglish Translation: {{ $json['\tEnglish Translation'] }}\nWord-by-Word Translation: {{ $json['Word-by-Word Translation'] }}\n\n\nGenerate a complete Telugu voiceover script following all the guidelines in the system message.", "options": {"systemMessage": "=You are a Telugu voiceover scriptwriter for Bhagavad Gita educational videos.\n\nTARGET AUDIENCE: All people seeking spiritual knowledge.\n\nINPUT & AUTHENTICITY REQUIREMENTS:\n• Expected input fields from n8n:\n  - Chapter-Verse: (format: \"number-number\" e.g., 1-3)\n  - Sanskrit Sloka:\n  - Romanized Transliteration:\n  - English Translation:\n  - Word-by-Word Translation:\n• Convert English numbers to Telugu alphabetic words in opening for better TTS pronunciation (e.g., 1=ఒకటి, 2=రెండు, 3=మూడు, etc.).\n• Verify Chapter-Verse, Sanskrit Sloka, and English Translation authenticity using your Bhagavad Gita knowledge.\n• Validate chapter (1-18) and verse numbers. Use authentic Sanskrit if provided text is incorrect.\n• Base explanations on authentic Sanskrit verses, not potentially incorrect translations.\n• If corrections needed, note: \"ఈ శ్లోకం యొక్క సరైన రూపం ఇది\"\n• Use traditional commentaries: <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> for interpretations.\n\nSCRIPT REQUIREMENTS:\n• Language: Simple Telugu, avoid complex Sanskrit terms\n• Tone: Warm, humble, reflective - like a caring spiritual elder\n• Character Limit: Telugu content 800-1200 characters\n• Maintain reverence and accuracy with <PERSON>'s teachings\n• Use simple words for Google TTS pronunciation\n• Standard Telugu spelling, avoid archaic forms\n\nSCRIPT STRUCTURE:\n1. OPENING (MUST BE SPOKEN FIRST - Opening Scene): \"Say in a warm, welcoming tone: ఈ వీడియోలో మనం భగవద్గీత అధ్యాయము [chapter in Telugu words], శ్లోకము [verse in Telugu words] ను పరిశీలిద్దాం.\"\n   • This opening greeting MUST be the very first thing spoken in the video\n   • Convert numbers to Telugu alphabetic words (1=ఒకటి, 2=రెండు, 3=మూడు, 4=నాలుగు, 5=ఐదు, 6=ఆరు, 7=ఏడు, 8=ఎనిమిది, 9=తొమ్మిది, 10=పది, etc.)\n   • Add chapter theme context and Arjuna's situation\n\n2. SANSKRIT SLOKA: \"Say with devotional reverence: మొదటగా ఈ పవిత్ర శ్లోకాన్ని భక్తితో విందాం.\"\n   • Present Sanskrit sloka in original form - never modify\n   • Use authentic verse if provided text is incorrect\n\n3. VERSE EXPLANATION: \"Say in a gentle, teaching tone: ఈ శ్లోకం అర్థం ఏమిటంటే…\"\n   • Connect to universal human experiences\n   • Use relatable examples while maintaining authenticity\n   • Explain karma, dharma, moksha in simple terms\n\n4. BHAVA (EMOTIONAL MOOD) OF THE VERSE: \"Say in a contemplative tone: ఈ శ్లోకంలో ఉన్న భావం మనకు ఏమి చెప్తుందంటే…\"\n   • Express the emotional essence (e.g., courage, detachment, surrender, wisdom)\n   • Keep it heartfelt and relatable\n\n5. PRACTICAL APPLICATION: \"Say in a caring, guidance tone: ఈ బోధనను మన దైనందిన జీవితంలో ఎలా ఉపయోగించుకోవచ్చు అంటే,\"\n   • Family, work, personal struggles, spiritual growth examples\n\n6. FINAL CLOSING: \"Say in a warm, encouraging tone: మరిన్ని భగవద్గీత శ్లోకాల అర్థాన్ని తెలుసుకోవాలంటే, మా తదుపరి వీడియోలను తప్పకుండా చూడండి.\"\n\nGOOGLE TTS OPTIMIZATION:\n• Use natural language prompts: \"Say in [style]:\" followed by content\n• TTS Prompts: \"Say in a warm tone:\", \"Say with reverence:\", \"Say contemplatively:\"\n• Break long sentences into shorter segments for clear pronunciation\n• Use simple Telugu vocabulary familiar to TTS engines\n• Maintain single-speaker consistency with varied emotional delivery\n\nOUTPUT FORMAT REQUIREMENTS:\n• Return as ONE continuous line with NO line breaks\n• Telugu characters: 800-1200 (count and adjust if needed)\n• NO quotation marks (\") - causes JSON parsing errors\n• NO backslashes (\\), curly braces {}, square brackets [], angle brackets <>\n• Use single quotes (') for contractions if needed\n• Convert direct quotes to indirect speech\n• Pure plain text suitable for JSON parsing\n\nFINAL VALIDATION:\n• Verify authentic Bhagavad Gita alignment\n• Confirm Sanskrit sloka authenticity\n• Check Telugu character count (800-1200)\n• Ensure JSON compatibility\n• Validate TTS readiness"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [256, 1936], "id": "a59b7434-3ad6-493b-b72c-7f6ce2d29465", "name": "AI Agent5"}, {"parameters": {"modelName": "models/gemini-2.5-pro", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [208, 2336], "id": "5effa768-43fd-41f1-bba2-b9e75e7a7759", "name": "Google Gemini Chat Model5", "credentials": {"googlePalmApi": {"id": "EyJAWdGeUe8P7fAh", "name": "22kb1a0521"}}}, {"parameters": {"options": {}}, "id": "1f3745a7-87b2-41c0-961b-fd15daba68c9", "name": "Process One Verse at a Time2", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-32, 1936]}, {"parameters": {"fileSelector": "={{ $json.output_file.replace('/app', '') }}", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [1552, 1936], "id": "fb0bf44e-cd49-45f0-a6c0-35469c3cdb7c", "name": "Read/Write Files from Disk20"}, {"parameters": {"method": "POST", "url": "https://api.groq.com/openai/v1/audio/transcriptions", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer ********************************************************"}]}, "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"name": "model", "value": "whisper-large-v3-turbo"}, {"name": "language", "value": "te"}, {"name": "response_format", "value": "verbose_json"}, {"name": "timestamp_granularities[]", "value": "word"}, {"parameterType": "formBinaryData", "name": "file", "inputDataFieldName": "=data"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1760, 1680], "id": "5f66045a-0c75-41c1-9b5f-36218581fcbd", "name": "HTTP Request31", "retryOnFail": true, "maxTries": 2}, {"parameters": {"jsCode": "// ===== CONFIGURATION VARIABLES =====\nconst CONFIG = {\n  // Number of words to display on screen at a time\n  wordsToShow: 4,             // Increased for better readability on mobile\n  \n  // General subtitle styling  \n  fontName: 'Noto Sans Telugu', // Better Telugu font support\n  fontSize: 24,               // Much smaller for 432x768 resolution\n  fontColor: 'FFFFFF',        // Regular text color in BGR format (white)\n  outlineColor: '000000',     // Text outline color in BGR format (black)\n  backgroundColor: '80000000', // Semi-transparent background for readability\n  \n  // Highlight styling\n  highlightFontSize: 28,      // Slightly larger for highlight\n  highlightColor: 'FFFF00',   // Bright yellow for better visibility (BGR format)\n  \n  // Text appearance\n  bold: 1,                    // Bold for better visibility\n  italic: 0,\n  underline: 0,\n  strikeout: 0,\n  \n  // Text scaling and positioning\n  scaleX: 100,\n  scaleY: 100,\n  spacing: 2,                 // Slight letter spacing for readability\n  angle: 0,\n  \n  // Border options\n  borderStyle: 1,\n  outline: 2,                 // Thinner outline for mobile\n  shadow: 1,\n  \n  // Alignment and margins (adjusted for 432x768)\n  alignment: 2,               // Bottom center\n  marginL: 10,\n  marginR: 10,\n  marginV: 150,               // Increased margin to avoid Instagram/YouTube UI elements\n  \n  // Video dimensions (updated for your 432x768 format)\n  videoWidth: 432,            // Updated to match your video resolution\n  videoHeight: 768,           // Updated to match your video resolution\n  \n  // Maximum gap time between words (in seconds) before considering it a new phrase\n  maxGapTimeMs: 1500,         // Slightly reduced for smoother transitions\n  \n  // Output file name\n  outputFileName: 'telugu_karaoke_subtitles.ass',\n  \n  // Subtitle display mode: 'karaoke' or 'simple'\n  displayMode: 'simple',      // Use 'simple' for better compatibility\n  \n  // Minimum display time for each subtitle (in seconds)\n  minDisplayTime: 0.5\n};\n\n// Parse input data - handle both array and direct object format\n// Fix for HTTP response that returns array format\nlet inputData;\nif (Array.isArray(items[0].json)) {\n  // HTTP response is array format: [{ \"task\": \"transcribe\", \"words\": [...] }]\n  inputData = items[0].json[0];\n} else if (items[0].json && items[0].json.words) {\n  // Direct object format: { \"words\": [...] }\n  inputData = items[0].json;\n} else {\n  // Alternative format where words might be directly in json\n  inputData = items[0].json;\n}\n\nconst words = inputData.words || [];\n\n// Debug logging to help troubleshoot\nconsole.log('Input data structure:', {\n  isArray: Array.isArray(items[0].json),\n  hasWords: !!inputData.words,\n  wordCount: words.length,\n  firstWord: words[0] ? words[0].word : 'none',\n  dataKeys: Object.keys(inputData)\n});\n\n// Validate words data\nif (!words || words.length === 0) {\n  return [{\n    json: {\n      error: 'No words found in input data',\n      inputStructure: typeof items[0].json,\n      inputKeys: items[0].json ? Object.keys(items[0].json) : 'null'\n    }\n  }];\n}\n\n// Validate word timing data\nconst validWords = words.filter(word => \n  word.word && \n  typeof word.start === 'number' && \n  typeof word.end === 'number' && \n  word.start >= 0 && \n  word.end > word.start\n);\n\nif (validWords.length === 0) {\n  return [{\n    json: {\n      error: 'No valid word timing data found',\n      wordCount: words.length,\n      sampleWord: words[0]\n    }\n  }];\n}\n\nconsole.log(`Processing ${validWords.length} valid words out of ${words.length} total words`);\n\n// ASS Header with proper formatting\nlet ass = `[Script Info]\nTitle: Telugu Karaoke Subtitles\nScriptType: v4.00+\nPlayResX: ${CONFIG.videoWidth}\nPlayResY: ${CONFIG.videoHeight}\n\n[V4+ Styles]\nFormat: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding\nStyle: Default,${CONFIG.fontName},${CONFIG.fontSize},&H${CONFIG.fontColor},&H${CONFIG.outlineColor},&H${CONFIG.outlineColor},&H${CONFIG.backgroundColor},${CONFIG.bold},${CONFIG.italic},${CONFIG.underline},${CONFIG.strikeout},${CONFIG.scaleX},${CONFIG.scaleY},${CONFIG.spacing},${CONFIG.angle},${CONFIG.borderStyle},${CONFIG.outline},${CONFIG.shadow},${CONFIG.alignment},${CONFIG.marginL},${CONFIG.marginR},${CONFIG.marginV},1\n\n[Events]\nFormat: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text\n`;\n\n/**\n * Helper: convert seconds to ASS time format (H:MM:SS.cs)\n */\nfunction formatTimeASS(sec) {\n  const h = Math.floor(sec / 3600);\n  const m = Math.floor((sec % 3600) / 60);\n  const s = Math.floor(sec % 60);\n  const cs = Math.floor((sec - Math.floor(sec)) * 100);\n  return `${h}:${m.toString().padStart(2, '0')}:${s.toString().padStart(2, '0')}.${cs.toString().padStart(2, '0')}`;\n}\n\n/**\n * Creates the subtitle text with the specified word highlighted within a fixed group\n * Simplified for better FFmpeg compatibility\n */\nfunction createSubtitleText(wordGroup, highlightIndex) {\n  let line = '';\n  \n  wordGroup.forEach((word, i) => {\n    // Add space between words (not before the first word)\n    if (i > 0) line += ' ';\n    \n    // Apply highlight to the specified word - simplified approach\n    if (i === highlightIndex) {\n      // Use simpler karaoke tags that work better with FFmpeg\n      line += `{\\\\k100}${word.word.trim()}`;\n    } else {\n      line += word.word.trim();\n    }\n  });\n  \n  return line;\n}\n\n/**\n * Alternative: Create simple subtitles without karaoke effects\n * Use this if karaoke doesn't work well\n */\nfunction createSimpleSubtitleText(wordGroup) {\n  return wordGroup.map(word => word.word.trim()).join(' ');\n}\n\n/**\n * Create groups of words that respect natural pauses and sentence boundaries\n * Improved for Telugu text\n */\nfunction createWordGroups(words) {\n  const groups = [];\n  let currentGroup = [];\n  \n  for (let i = 0; i < words.length; i++) {\n    const currentWord = words[i];\n    currentGroup.push(currentWord);\n    \n    // Check if this word ends with punctuation (sentence boundary)\n    const endsWithPunctuation = /[।.!?॥]$/.test(currentWord.word.trim());\n    \n    // Calculate gap to next word\n    const nextWord = words[i + 1];\n    const gap = nextWord ? (nextWord.start - currentWord.end) : 0;\n    \n    // Start a new group if:\n    // 1. We've reached the maximum words per group, OR\n    // 2. We've encountered punctuation (end of sentence), OR\n    // 3. There's a significant pause (> 1 second), OR\n    // 4. We're at the last word\n    if (currentGroup.length >= CONFIG.wordsToShow || \n        endsWithPunctuation || \n        gap > 1.0 || \n        i === words.length - 1) {\n      if (currentGroup.length > 0) {\n        groups.push([...currentGroup]);\n        currentGroup = [];\n      }\n    }\n  }\n  \n  // Add any remaining words\n  if (currentGroup.length > 0) {\n    groups.push(currentGroup);\n  }\n  \n  return groups;\n}\n\n// Create word groups that respect natural speech patterns\nconst wordGroups = createWordGroups(validWords);\nconst dialogueLines = [];\n\nif (CONFIG.displayMode === 'simple') {\n  // Simple mode: show each word group as a complete subtitle\n  for (let groupIndex = 0; groupIndex < wordGroups.length; groupIndex++) {\n    const wordGroup = wordGroups[groupIndex];\n    \n    if (wordGroup.length === 0) continue;\n    \n    const startTime = wordGroup[0].start;\n    const endTime = Math.max(wordGroup[wordGroup.length - 1].end, startTime + CONFIG.minDisplayTime);\n    const formattedStart = formatTimeASS(startTime);\n    const formattedEnd = formatTimeASS(endTime);\n    const subtitleText = createSimpleSubtitleText(wordGroup);\n    \n    dialogueLines.push({\n      start: startTime,\n      end: endTime,\n      formattedStart,\n      formattedEnd,\n      text: subtitleText,\n      group: groupIndex\n    });\n  }\n} else {\n  // Karaoke mode: highlight individual words (original logic)\n  for (let i = 0; i < validWords.length; i++) {\n    const currentWord = validWords[i];\n    \n    // Find which group contains this word\n    let groupIndex = -1;\n    let positionIndex = -1;\n    \n    for (let g = 0; g < wordGroups.length; g++) {\n      const wordIndex = wordGroups[g].findIndex(w => \n        w.start === currentWord.start && w.end === currentWord.end && w.word === currentWord.word);\n      \n      if (wordIndex !== -1) {\n        groupIndex = g;\n        positionIndex = wordIndex;\n        break;\n      }\n    }\n    \n    if (groupIndex === -1) continue;\n    \n    const wordGroup = wordGroups[groupIndex];\n    \n    // Create subtitle with the correct word highlighted\n    const startTime = currentWord.start;\n    const endTime = Math.max(currentWord.end, startTime + CONFIG.minDisplayTime);\n    const formattedStart = formatTimeASS(startTime);\n    const formattedEnd = formatTimeASS(endTime);\n    const subtitleText = createSubtitleText(wordGroup, positionIndex);\n    \n    // Add this subtitle to our list\n    dialogueLines.push({\n      start: startTime,\n      end: endTime,\n      formattedStart,\n      formattedEnd,\n      text: subtitleText,\n      group: groupIndex,\n      position: positionIndex\n    });\n  }\n}\n\n// Fill gaps between words in the same group to maintain visual continuity\nfor (let i = 0; i < dialogueLines.length - 1; i++) {\n  const current = dialogueLines[i];\n  const next = dialogueLines[i + 1];\n  \n  // If they're in the same group and there's a small gap\n  if (current.group === next.group && \n      next.start - current.end > 0 && \n      next.start - current.end <= CONFIG.maxGapTimeMs / 1000) {\n    // Fill the gap by extending the current line's end time\n    current.end = next.start;\n    current.formattedEnd = formatTimeASS(current.end);\n  }\n}\n\n// Validate that the total audio duration is at least 1 minute (60 seconds)\nconst minAudioDuration = 60; // seconds (1 minute)\nconst totalAudioDuration = Math.max(...dialogueLines.map(line => line.end));\n\nif (totalAudioDuration < minAudioDuration) {\n  return [{\n    json: {\n      error: 'Audio duration is less than minimum required length',\n      minRequiredDuration: minAudioDuration,\n      actualDuration: totalAudioDuration,\n      actualDurationFormatted: formatTimeASS(totalAudioDuration),\n      message: `Audio duration is ${totalAudioDuration.toFixed(2)}s (${formatTimeASS(totalAudioDuration)}), but minimum required is ${minAudioDuration}s (1 minute). Workflow stopped.`\n    }\n  }];\n}\n\n// Validate that all timestamps are within the 174-second limit\nconst maxAllowedTime = 174; // seconds\nconst exceedingLines = dialogueLines.filter(line => line.end > maxAllowedTime);\n\nif (exceedingLines.length > 0) {\n  const firstExceedingLine = exceedingLines[0];\n  return [{\n    json: {\n      error: 'Subtitle timing exceeds maximum allowed duration',\n      maxAllowedTime: maxAllowedTime,\n      exceedingTimestamp: firstExceedingLine.formattedEnd,\n      exceedingTimeSeconds: firstExceedingLine.end,\n      exceedingText: firstExceedingLine.text,\n      totalExceedingLines: exceedingLines.length,\n      message: `Subtitle at ${firstExceedingLine.formattedEnd} (${firstExceedingLine.end.toFixed(2)}s) exceeds the ${maxAllowedTime}s limit. Workflow stopped.`\n    }\n  }];\n}\n\n// Write all dialogue lines to the ASS file\ndialogueLines.forEach(line => {\n  ass += `Dialogue: 0,${line.formattedStart},${line.formattedEnd},Default,,0,0,0,,${line.text}\\n`;\n});\n\n// Add debug information as comments\nass += `\\n; Generated ASS file info:\\n`;\nass += `; Mode: ${CONFIG.displayMode}\\n`;\nass += `; Resolution: ${CONFIG.videoWidth}x${CONFIG.videoHeight}\\n`;\nass += `; Total dialogue lines: ${dialogueLines.length}\\n`;\nass += `; Word groups: ${wordGroups.length}\\n`;\nass += `; Font: ${CONFIG.fontName}, Size: ${CONFIG.fontSize}\\n`;\n\n// Note: File system operations are not available in n8n Code node\n// The ASS file will be returned as binary data for n8n to handle\nlet fileSaveStatus = 'handled_by_n8n';\nlet fileSavePath = 'returned_as_binary_data';\n\nconsole.log(`✅ ASS file generated successfully (${ass.length} characters)`);\nconsole.log(`📄 File will be available as binary data in n8n workflow`);\n\n// Create base64 encoded string for n8n\nconst assBase64 = Buffer.from(ass, 'utf8').toString('base64');\n\n// Return as binary ASS file with improved metadata\nreturn [{\n  binary: {\n    data: {\n      data: assBase64,\n      mimeType: 'text/ass',\n      fileName: CONFIG.outputFileName\n    }\n  },\n  json: {\n    success: true,\n    mode: CONFIG.displayMode,\n    resolution: `${CONFIG.videoWidth}x${CONFIG.videoHeight}`,\n    dialogueLines: dialogueLines.length,\n    wordGroups: wordGroups.length,\n    totalWords: validWords.length,\n    originalWords: words.length,\n    fileSaved: fileSaveStatus,\n    filePath: fileSavePath,\n    fileSize: ass.length,\n    info: `Generated ASS file with ${dialogueLines.length} subtitle lines for ${CONFIG.videoWidth}x${CONFIG.videoHeight} video`,\n    note: 'ASS file returned as binary data - use n8n Write Binary File node to save to disk'\n  }\n}];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1984, 1952], "id": "b4390a96-4730-4fef-9b84-d5c418f9cb0f", "name": "Code10"}, {"parameters": {"operation": "write", "fileName": "=/shared-data/ass-scripts/{{ $node[\"Read/Write Files from Disk20\"].json[\"fileName\"].replace('.mp3', '.ass') }}", "dataPropertyName": "=data", "options": {"append": false}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [2192, 1952], "id": "87302450-305b-4a93-b7a7-1dc7ea7d564e", "name": "Save ass2"}, {"parameters": {"method": "POST", "url": "http://video-service:3002/create-enhanced-video", "sendBody": true, "bodyParameters": {"parameters": [{"name": "audioFileName", "value": "={{ $('Read/Write Files from Disk20').item.json.fileName }}"}, {"name": "assFileName", "value": "={{ $('Save ass2').item.json.fileName.split('/').pop()}}"}, {"name": "backgroundImage", "value": "={{ $json.fileName.split('/').pop() }}"}]}, "options": {"response": {"response": {"neverError": true, "responseFormat": "json"}}, "timeout": 900000}}, "id": "35e891fe-6cff-4dc9-9e51-73daace3dc87", "name": "Create Video1", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [2432, 3120], "executeOnce": true}, {"parameters": {"content": "🔗 Health check: http://localhost:3002/health\n🎬 Enhanced video endpoint: http://localhost:3002/create-enhanced-video\n\n✨ Features:\n  • 9:16 Vertical Video Support (432x768)\n  • ASS Subtitle Integration with Karaoke Highlighting\n  • Automatic File Location by Filename\n  • Enhanced Background Image Processing\n  • Multi-Image Support with Fade Transitions (a,b,c,d,e,f,g variants)", "height": 360}, "type": "n8n-nodes-base.stickyNote", "position": [-2352, 3360], "typeVersion": 1, "id": "ed59bc65-db0d-4dd6-a5b5-b7ee5f5ac296", "name": "Sticky Note6"}, {"parameters": {"promptType": "define", "text": "=Generate optimized YouTube Shorts metadata for a Bhagavad Gita video based on the following verse details. \n\nCreate engaging Telugu-language content optimized for YouTube Shorts algorithm and Telugu spiritual audience:\n\n**Bhagavad Gita Verse Details:**\nChapter-Verse: {{ $('Process One Verse at a Time2').item.json['Chapter-Verse'] }}\nSanskrit Sloka: {{ $('Process One Verse at a Time2').item.json['Sanskrit Shloka'] }}\nRomanized Transliteration: {{ $('Process One Verse at a Time2').item.json['Romanized Transliteration'] }}\nEnglish Translation: {{ $('Process One Verse at a Time2').item.json['\tEnglish Translation'] }}\nWord-by-Word Translation: {{ $('Process One Verse at a Time2').item.json['Word-by-Word Translation'] }}\n\n**Instructions:**\n1. Generate ONLY the Telugu title and one viral hashtag (NO chapter/verse numbers - these are configured in n8n)\n2. Create an engaging hook (first 1-2 lines) in both Telugu and English\n3. Include relevant tags and trending hashtags\n4. Return only valid JSON format as specified in the system prompt\n\n**Note:** Do not include chapter/verse numbers, #bhagavadgita, or #shorts in the title as these are already configured in n8n YouTube node. Focus on making the content viral and spiritually engaging for Telugu audience.\n", "hasOutputParser": true, "options": {"systemMessage": "=\nYou are an expert YouTube Shorts metadata generator specialized in Telugu spiritual content, specifically Bhagavad Gita verses.\n\nYour task is to analyze the provided Bhagavad Gita verse details and return a JSON object with optimized metadata for YouTube Shorts algorithm.\n\n## TITLE REQUIREMENTS:\n- Generate ONLY: \"{Short Telugu Title} {One Relevant Viral Hashtag}\"\n- DO NOT include chapter/verse numbers (భగవద్గీత 1.1:) - this is already configured in n8n\n- DO NOT include #bhagavadgita #shorts - these are already added in n8n YouTube node\n- The Telugu title should be short, emotionally engaging, and sound like natural spoken Telugu (avoid overly literary or formal phrasing)\n- Do NOT use punctuation-heavy titles or generic lines like \"శ్లోక వివరణ\"\n- Include ONE viral hashtag related to the verse theme (like #motivation #moralstories #history #lifelessons #wisdom #surrender #detachment #karma #dharma etc.)\n- Keep the title under 60 characters total, including the hashtag\n- Examples of good titles:\n  - \"కర్మ చేయండి ఫలితం ఆశించవద్దు #motivation\"\n  - \"సర్వధర్మాన్ పరిత్యజ్య #surrender\"\n  - \"యుగే యుగే అవతారం #divine\"\n\n## DESCRIPTION REQUIREMENTS:\nGenerate only the HOOK section (first 1-2 lines) that appears before \"Show More\":\n- Create a compelling hook in both Telugu and English on separate lines\n- This hook should be based on the specific chapter and verse content\n- Format: \"[Relevant emoji based on verse theme] [Telugu hook line about the verse's power/wisdom/impact]\\n[English hook line about the meaning/benefit] [Relevant emoji based on verse theme]\"\n- Choose emojis that match the verse's core teaching (e.g., ⚔️ for battle/duty, 🕉️ for spiritual wisdom, 🎯 for focus/karma, � for strength, 🌟 for divine knowledge, 🧘 for meditation/peace, ❤️ for devotion, etc.)\n- Example: \"⚔️ ఈ భగవద్గీత శ్లోకం మీ కర్తవ్య బోధను స్పష్టం చేస్తుంది!\\nDiscover the warrior's path to righteous action ⚔️\"\n- The Telugu and English hook lines together must not exceed 125 characters\n- Avoid reusing the title in the description—make the hook fresh and different\n- Make it emotionally engaging and curiosity-inducing\n- DO NOT include Sanskrit sloka, translations, social media links, or any other content as these are already set in n8n\n\n\n\n## SEARCH-BASED PHRASE:\n- Generate one **natural, keyword-rich sentence** using common words, teachings, or values **directly related to the verse content**\n- It should include terms that users are likely to search for (based on verse theme) such as \"karma,\" \"dharma,\" \"surrender,\" \"soul,\" \"Krishna's teaching,\" etc.\n- Avoid hashtags or unnatural keyword stuffing\n- Write it in **simple English**, readable and meaningful as part of the video description\n- Limit to **200 characters max**\n- Example: \"This verse teaches the essence of karma yoga—doing one's duty without attachment, as explained by Lord Krishna in the battlefield of Kurukshetra.\"\n\n## TRENDING HASHTAGS:\n- Generate **5–7 highly relevant, trending hashtags** based on the verse's core teaching (e.g., karma, detachment, surrender, devotion, soul, dharma)\n- Focus on **spiritual, motivational, and philosophical** themes\n- Mix of **emotion-driven**, **action-based**, and **value-centered** hashtags (e.g., motivation, wisdom, innerpeace, karma, devotion, consciousness)\n- **Avoid duplicates** with the hashtag already used in the title\n- Do NOT use generic tags like shorts or BhagavadGita — these are already handled\n- Format as a **single string** with hashtags separated by spaces, each prefixed with #\n- All hashtags must be **one word only**, lowercase, and relevant\n- Example output: `\"#wisdom #innerpeace #karma #lifelessons #mindfulness #dharma #spiritualgrowth\"`\n\n## OUTPUT FORMAT:\nReturn ONLY a valid JSON object with these exact fields:\n- title: Only the Telugu title and viral hashtag (NO chapter/verse numbers)\n- description: Only the hook section (first 1-2 lines)\n- searchSummary: Natural, keyword-rich sentence related to the verse content\n- trendingHashtags: Single string with hashtags separated by spaces (e.g., \"#wisdom #karma #innerpeace\")\n\n## IMPORTANT RULES:\n- Return ONLY JSON, no explanations or extra text\n- No conversational elements or greetings\n- Ensure JSON is valid and parseable\n- Response must start with { and end with }\n- Focus on making content viral and engaging for Telugu spiritual audience\n- Consider the specific verse meaning when creating hooks and hashtags\n- Make the content emotionally resonant and spiritually uplifting\n- All field values must be clean strings or arrays. Do not include special formatting, markdown, or nested structures\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [3008, 2704], "id": "8e2a31b5-3446-491c-8882-b776bd1afa84", "name": "AI Agent6", "executeOnce": true}, {"parameters": {"modelName": "models/gemini-2.5-pro", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [2896, 3056], "id": "53dab227-695c-4e07-a8f7-2fb4d5760a3c", "name": "Google Gemini Chat Model6", "credentials": {"googlePalmApi": {"id": "DMsy54sgaX77lna0", "name": "gudam"}}}, {"parameters": {"jsonSchemaExample": "{\n  \"title\": \"కర్మ చేయండి ఫలితం ఆశించవద్దు #motivation\",\n  \"description\": \"💡 ఈ భగవద్గీత శ్లోకం మీ జీవితంలో ఒత్తిడిని తగ్గిస్తుంది!\\nDiscover the secret to stress-free action and inner peace 🙏\",\n  \"searchSummary\": \"This verse explains the path of detachment and karma yoga, as taught by <PERSON> to <PERSON><PERSON><PERSON><PERSON> in the midst of confusion and fear.\",\n  \"trendingHashtags\": \"#wisdom #innerpeace #karma #lifelessons #mindfulness #dharma #spiritualgrowth\"\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [3296, 3040], "id": "f090ee76-f142-4c24-98bf-4e163fa91cc2", "name": "Structured Output Parser"}, {"parameters": {"fileSelector": "={{ $('Read/Write Files from Disk39').item.json.fileName.replace('images', 'videos').replace('.jpg', '.mp4') }}", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [3488, 2832], "id": "afe274f9-1501-44b5-83f2-9e2af481bb17", "name": "Read/Write Files from Disk29", "executeOnce": true}, {"parameters": {"method": "POST", "url": "=https://www.googleapis.com/upload/youtube/v3/thumbnails/set?videoId={{ $('Upload a video').item.json.uploadId }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "youTubeOAuth2Api", "sendBody": true, "contentType": "binaryData", "inputDataFieldName": "data", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [4256, 3824], "id": "8ac5a7f5-f9aa-4929-9be1-db4007826153", "name": "HTTP Request41", "credentials": {"youTubeOAuth2Api": {"id": "V0poigWaAdu7vVo7", "name": "bhagavd gita"}}}, {"parameters": {"fileSelector": "/shared-data/media/image.png", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [3920, 3840], "id": "be8b05bf-9235-48d8-ba23-703a58284b7e", "name": "Read/Write Files from Disk30"}, {"parameters": {"content": "```\n╔════════════════════════════════════════════════════╗\n║                                                    ║\n║  📸 THESE TWO NODES ARE USED TO SET THUMBNAIL FOR  ║\n║           UPLOADED YOUTUBE VIDEO                   ║\n║                                                    ║\n╚════════════════════════════════════════════════════╝\n```\n", "width": 600}, "type": "n8n-nodes-base.stickyNote", "position": [3856, 3632], "typeVersion": 1, "id": "f09be7d1-f649-4278-89a3-f904018197d3", "name": "Sticky Note7"}, {"parameters": {"operation": "write", "fileName": "=/shared-data/images/{{ $('Save ass2').item.json.fileName.replace('/shared-data/ass-scripts/', '').replace('.ass', '.jpg') }}", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [1056, 3232], "id": "5269d015-17b2-4ad9-92d4-4784fbc9fb1f", "name": "Read/Write Files from Disk39"}, {"parameters": {"operation": "write", "fileName": "=/shared-data/images/{{ $('Save ass2').item.json.fileName.replace('/shared-data/ass-scripts/', '').replace('.ass', 'b.jpg') }}", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [1120, 3760], "id": "b3df3347-d717-452b-a9a7-8f88be13e02a", "name": "Read/Write Files from Disk40"}, {"parameters": {"operation": "write", "fileName": "=/shared-data/images/{{ $('Save ass2').item.json.fileName.replace('/shared-data/ass-scripts/', '').replace('.ass', 'c.jpg') }}", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [1120, 4032], "id": "46c38b77-ded6-4beb-916d-ec66a0f68b58", "name": "Read/Write Files from Disk41"}, {"parameters": {"operation": "write", "fileName": "=/shared-data/images/{{ $('Save ass2').item.json.fileName.replace('/shared-data/ass-scripts/', '').replace('.ass', 'f.jpg') }}", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [1136, 4848], "id": "0a0c1a19-65ed-4029-a7b7-b69846bc46c4", "name": "Read/Write Files from Disk42"}, {"parameters": {"operation": "write", "fileName": "=/shared-data/images/{{ $('Save ass2').item.json.fileName.replace('/shared-data/ass-scripts/', '').replace('.ass', 'g.jpg') }}", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [1136, 5104], "id": "6549a1f0-5aa7-4de0-b169-147829a0e4bd", "name": "Read/Write Files from Disk43"}, {"parameters": {"operation": "write", "fileName": "=/shared-data/images/{{ $('Save ass2').item.json.fileName.replace('/shared-data/ass-scripts/', '').replace('.ass', 'd.jpg') }}", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [1120, 4288], "id": "f1ffd906-d8f3-4e8d-97c9-78014fe0a565", "name": "Read/Write Files from Disk44"}, {"parameters": {"operation": "write", "fileName": "=/shared-data/images/{{ $('Save ass2').item.json.fileName.replace('/shared-data/ass-scripts/', '').replace('.ass', 'e.jpg') }}", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [1136, 4592], "id": "5de6a2dc-e70f-441e-87c6-3fad07b7e304", "name": "Read/Write Files from Disk45"}, {"parameters": {"operation": "write", "fileName": "=/shared-data/images/{{ $('Save ass2').item.json.fileName.replace('/shared-data/ass-scripts/', '').replace('.ass', 'a.jpg') }}", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [1072, 3488], "id": "239d351e-42bf-4ab2-9f85-0ffdfb7ec3ab", "name": "Read/Write Files from Disk46"}, {"parameters": {"numberInputs": 8}, "type": "n8n-nodes-base.merge", "typeVersion": 3.2, "position": [1952, 3280], "id": "c4f2055a-aeed-4078-96c5-c5c9d4a17bc0", "name": "Merge4", "executeOnce": false}, {"parameters": {"operation": "toBinary", "sourceProperty": "predictions[0].bytesBase64Encoded", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [656, 3264], "id": "5d0fd752-9ae7-492a-9730-b2a7c7219b43", "name": "Convert to File8"}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/imagen-3.0-generate-002:predict?key=AIzaSyARDGaCg7Ddw9jORagq4fA1N9WSbi4xT3I", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"instances\": [\n    {\n      \"prompt\": \"{{ $json.output.variations[0] }}\"\n    }\n  ],\n  \"parameters\": {\n    \"numberOfImages\": 1,\n    \"aspectRatio\": \"9:16\",\n    \"personGeneration\": \"allow_all\"\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [192, 3984], "id": "d4f1cec6-c40a-48af-893d-ce29ca31384a", "name": "HTTP Request46", "retryOnFail": true, "maxTries": 2}, {"parameters": {"operation": "toBinary", "sourceProperty": "predictions[0].bytesBase64Encoded", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [688, 3840], "id": "da931c29-54bf-48c6-aacb-1e2f00ec6781", "name": "Convert to File9"}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/imagen-3.0-generate-002:predict?key=AIzaSyARDGaCg7Ddw9jORagq4fA1N9WSbi4xT3I", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"instances\": [\n    {\n      \"prompt\": \"{{ $json.output.variations[4] }}\"\n    }\n  ],\n  \"parameters\": {\n    \"numberOfImages\": 1,\n    \"aspectRatio\": \"9:16\",\n    \"personGeneration\": \"allow_all\"\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [336, 4944], "id": "7ecd0390-b4fb-4a07-85de-14df05b290e6", "name": "HTTP Request62", "retryOnFail": true, "maxTries": 2}, {"parameters": {"operation": "toBinary", "sourceProperty": "predictions[0].bytesBase64Encoded", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [752, 4912], "id": "eb2353bc-c1c3-447e-ae6f-8937bbccc195", "name": "Convert to File10"}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/imagen-3.0-generate-002:predict?key=AIzaSyARDGaCg7Ddw9jORagq4fA1N9WSbi4xT3I", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"instances\": [\n    {\n      \"prompt\": \"{{ $json.output.variations[5] }}\"\n    }\n  ],\n  \"parameters\": {\n    \"numberOfImages\": 1,\n    \"aspectRatio\": \"9:16\",\n    \"personGeneration\": \"allow_all\"\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [336, 5152], "id": "9f8ec66a-ccdf-4e08-82f9-fb339986340a", "name": "HTTP Request63", "retryOnFail": true, "maxTries": 2}, {"parameters": {"operation": "toBinary", "sourceProperty": "predictions[0].bytesBase64Encoded", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [736, 5120], "id": "c460ed6d-3111-44f9-bdee-238bfc4ecf99", "name": "Convert to File11"}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/imagen-3.0-generate-002:predict?key=AIzaSyARDGaCg7Ddw9jORagq4fA1N9WSbi4xT3I", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"instances\": [\n    {\n      \"prompt\": \"{{ $json.output }}\"\n    }\n  ],\n  \"parameters\": {\n    \"numberOfImages\": 1,\n    \"aspectRatio\": \"9:16\",\n    \"personGeneration\": \"allow_all\"\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [192, 3728], "id": "e1f583fc-b9d3-47c9-b98d-79a93a3985af", "name": "HTTP Request64", "retryOnFail": true, "maxTries": 2}, {"parameters": {"operation": "toBinary", "sourceProperty": "predictions[0].bytesBase64Encoded", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [672, 3584], "id": "fabda6b4-1b7f-4b4d-9599-d7f4bdc94f72", "name": "Convert to File20"}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/imagen-3.0-generate-002:predict?key=AIzaSyARDGaCg7Ddw9jORagq4fA1N9WSbi4xT3I", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"instances\": [\n    {\n      \"prompt\": \"{{ $json.output.variations[2] }}\"\n    }\n  ],\n  \"parameters\": {\n    \"numberOfImages\": 1,\n    \"aspectRatio\": \"9:16\",\n    \"personGeneration\": \"allow_all\"\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [336, 4464], "id": "8a051d52-07a9-4e27-b224-65abb9867b64", "name": "HTTP Request65", "retryOnFail": true, "maxTries": 2}, {"parameters": {"operation": "toBinary", "sourceProperty": "predictions[0].bytesBase64Encoded", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [736, 4448], "id": "ebbdbe32-a5b0-4207-96be-de23ea88737d", "name": "Convert to File21"}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/imagen-3.0-generate-002:predict?key=AIzaSyARDGaCg7Ddw9jORagq4fA1N9WSbi4xT3I", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"instances\": [\n    {\n      \"prompt\": \"{{ $json.output.variations[3] }}\"\n    }\n  ],\n  \"parameters\": {\n    \"numberOfImages\": 1,\n    \"aspectRatio\": \"9:16\",\n    \"personGeneration\": \"allow_all\"\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [336, 4704], "id": "6053b6ef-0bfc-4ac9-9c16-09257b7184ea", "name": "HTTP Request66", "retryOnFail": true, "maxTries": 2}, {"parameters": {"operation": "toBinary", "sourceProperty": "predictions[0].bytesBase64Encoded", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [736, 4672], "id": "df45e5d5-e503-4195-85bc-e44eb884bdbb", "name": "Convert to File22"}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/imagen-3.0-generate-002:predict?key=AIzaSyARDGaCg7Ddw9jORagq4fA1N9WSbi4xT3I", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"instances\": [\n    {\n      \"prompt\": \"{{ $json.output.variations[1] }}\"\n    }\n  ],\n  \"parameters\": {\n    \"numberOfImages\": 1,\n    \"aspectRatio\": \"9:16\",\n    \"personGeneration\": \"allow_all\"\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [336, 4192], "id": "e1b80d93-f831-4bbc-9101-7439b706dd99", "name": "HTTP Request67", "retryOnFail": true, "maxTries": 2}, {"parameters": {"operation": "toBinary", "sourceProperty": "predictions[0].bytesBase64Encoded", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [688, 4144], "id": "50023165-f17f-4c5f-addf-8698cad2e3fb", "name": "Convert to File23"}, {"parameters": {"promptType": "define", "text": "=Telugu Bhagavad Gita Verse script:\n{{ $('AI Agent5').item.json.output }}", "options": {"systemMessage": "You are a masterful image generation engine specializing in creating vivid, culturally authentic visual prompts from Bhagavad Gita content. Convert TTS voiceover scripts into single-line image generation prompts optimized for Google Gemini 1.5 Pro understanding and Imagen 3 image generation integration.\n\n### Input Processing\n**Receive:** TTS voiceover script with Telugu verse + explanation\n**Filter Out:** Voice directives (\"Say in warm tone:\", \"[pause]\", timing cues, bracketed audio instructions)\n**Extract:** Characters, settings, emotions, philosophical themes, cultural context\n\n### Core Requirements\n- **Format:** Single-line prompt with rich detail, vertical portrait orientation\n- **Style:** Photorealistic, cinematic quality, culturally authentic Mahabharata-era details\n- **Content:** Synthesize verse meaning + explanatory context into comprehensive visual narrative\n- **Avoid:** Text in images, literal interpretation of abstract concepts\n\n### Critical Visual Translation Rules\n**Sanskrit Terms → Visual Metaphors:**\n- Atma = radiant inner light, Buddhi = guiding flame, Dharma = flowing river\n- Karma = interconnected threads, Moksha = breaking chains/ascending light\n- Sam<PERSON>hi = perfect stillness with radiant aura, Shakti = dynamic flowing energy\n\n**Character Guidelines & Duplication Prevention:**\n- **Single Instance Rule:** Each character appears only ONCE unless verse explicitly requires multiple appearances\n- **Precise Positioning:** Specify exact spatial placement (left/right, foreground/background, sitting/standing)\n- **Complete Anatomy:** Use \"complete figure with all limbs visible\" where relevant. If a character is seated or partially behind an object, render only visible limbs naturally and clearly\n- **Clear Separation:** Maintain distinct character boundaries to prevent merging or overlap\n- **Context-Aware:** Rely on Google Gemini 1.5 Pro's built-in understanding of Bhagavad Gita context for character authenticity\n- **Spatial Relationships:** Use specific positioning terms: \"centered\", \"left foreground\", \"right background\", \"facing each other\", \"side by side\"\n- Clear hierarchy, specific facial expressions, body positions, interactions\n\n### Positioning Examples & Best Practices\n**Example Compositions:**\n- \"Krishna standing confidently in the center foreground, Arjuna kneeling respectfully to the left background\"\n- \"Single majestic Krishna clearly visible as the central figure, no duplicate figures\"\n- \"Arjuna seated cross-legged with both legs fully visible, hands resting on knees\"\n\n**Critical Rules:**\n- **Full Body Visibility:** Ensure complete character representation: \"full body visible\", \"seated with legs clearly shown\", \"standing with feet firmly planted\"\n- **Character Separation:** Maintain clear visual separation between characters to prevent merging: \"distinctly positioned\", \"clearly separated figures\"\n\n**Atmospheric Enhancement:**\n- Cinematic lighting: \"volumetric lighting\", \"golden hour cinematography\", \"natural sunlight\"\n- Dynamic elements: \"flowing silk robes\", \"wind-blown hair\", \"atmospheric mist\"\n- Weather integration: \"storm clouds gathering\", \"dust particles in air\"\n\n**Quality Modifiers:**\n- \"4K HDR DSLR-quality cinematography\", \"photorealistic\", \"culturally authentic\"\n- \"Historically accurate\", \"emotionally resonant\", \"masterful composition\"\n- \"Shot on 50mm lens\", \"shallow depth of field\", \"cinematic scene\"\n\n### Processing Steps\n1. **Clean Input:** Remove TTS voice directives and audio cues\n2. **Extract Visuals:** Identify characters, settings, emotions, Sanskrit terms\n3. **Convert Concepts:** Transform abstract/Sanskrit terms into visual metaphors\n4. **Synthesize:** Combine verse foundation with explanatory enhancement\n5. **Structure:** Apply Subject + Action + Setting + Emotion + Style + Quality framework\n6. **Optimize:** Create comprehensive prompts tailored for Google Imagen 3's capabilities\n\n### Material & Cultural Realism\n- **Model Understanding:** Trust Google Gemini 1.5 Pro's built-in knowledge of Mahabharata-era cultural and visual context\n- Natural materials: \"weathered gold\", \"carved stone\", \"handwoven fabric patterns\"\n- Color palette: \"warm earth tones\", \"natural skin tones\", \"realistic textures\"\n- Spiritual symbols: \"realistic lotus flowers\", \"weathered golden chariots\", \"natural light rays\"\n\n### Google Imagen 3 Optimization Framework\n**Prompt Micro-Framework Structure:**\nFollow this precise flow: **Subject + Action + Setting + Emotion + Style + Quality**\n\n**Example Structure (Extended for Detail):**\n- **Subject:** \"Single majestic Krishna as divine charioteer with serene blue skin, flowing black hair adorned with peacock feather, wearing golden yellow silk dhoti with intricate embroidered borders\" \n- **Action:** \"standing in contemplative pose with raised right hand in blessing gesture, left hand gently holding chariot reins, weight shifted slightly forward in authoritative stance\"\n- **Setting:** \"on sacred Kurukshetra battlefield with ornate golden war chariots, white horses with decorative harnesses, distant army formations under dramatic storm clouds, ancient banners fluttering in wind\"\n- **Emotion:** \"radiating divine wisdom and serene authority, compassionate eyes reflecting infinite knowledge, peaceful countenance despite battlefield chaos, aura of spiritual transcendence\"\n- **Style:** \"cinematic composition with volumetric lighting streaming through storm clouds, golden hour backlighting creating dramatic silhouettes, shallow depth of field focusing on Krishna\"\n- **Quality:** \"4K HDR DSLR-quality photorealistic cinematography shot on 50mm lens, complete figure with all limbs visible, authentic Mahabharata-era cultural details, realistic fabric textures and weathered metal armor\"\n\n**Integration Note:** Output prompts leverage Google Gemini 1.5 Pro's extensive Bhagavad Gita knowledge for understanding and Google Imagen 3's advanced text-to-image generation capabilities, automatically applying appropriate cultural context, spiritual symbolism, and cinematic composition.\n\n🎯 **Output Requirements:** \n**Length:** Minimum 400 characters for comprehensive detail - leverage Google Imagen 3's capability for rich, detailed prompts.\n**Content:** Provide only the optimized image generation prompt in a single line without line breaks, explanations, or introductory text. \n**Structure:** Use Subject + Action + Setting + Emotion + Style + Quality framework with extensive detail for each component.\n**Detail Level:** Include comprehensive descriptions of facial expressions, body language, clothing textures, environmental elements, lighting effects, atmospheric conditions, and cultural authenticity markers.\n**Critical:** Ensure each character appears only once with precise positioning and complete anatomy. Output is specifically designed for Google Imagen 3 text-to-image generation to achieve maximum visual impact and cultural authenticity.\n\n### Divine Form Guidelines\n**Krishna's Appearance Context:**\n- **Regular Form:** Human form with two arms (default for most verses)\n- **Divine Revelation:** Four-armed Vishnu form only when verse mentions divine manifestation\n- **Cosmic Form:** Multi-armed universal form (Vishvarupa) only for cosmic vision verses\n- **Model Understanding:** Trust Google Gemini 1.5 Pro's built-in knowledge of Mahabharata-era cultural and visual context will apply appropriate details automatically\n\n### Detailed Output Enhancement Guidelines\n**Comprehensive Descriptions Required:**\n- **Character Details:** Physical attributes, clothing textures, jewelry, facial expressions, posture specifics\n- **Environmental Elements:** Architecture details, landscape features, atmospheric conditions, natural phenomena\n- **Lighting Specifications:** Multiple light sources, shadows, reflections, time of day effects\n- **Material Descriptions:** Fabric textures, metal finishes, stone surfaces, natural elements\n- **Cultural Authenticity:** Specific Mahabharata-era details, traditional symbols, historical accuracy\n- **Atmospheric Enhancement:** Weather effects, particles in air, environmental mood, spiritual aura\n\n**Minimum Detail Requirements:**\n- Each framework component (Subject, Action, Setting, Emotion, Style, Quality) should contain multiple descriptive elements\n- Include specific sensory details that enhance visual richness\n- Provide texture, color, and material specifications for all visible elements\n- Describe spatial relationships and composition thoroughly\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [-992, 4224], "id": "25f645a2-19f7-41e2-93d2-25b4758aa5c5", "name": "AI Agent9"}, {"parameters": {"modelName": "models/gemini-2.5-pro", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [-1232, 4544], "id": "3abe4da0-2cf0-4e18-82c6-2131b82029db", "name": "Google Gemini Chat Model9", "credentials": {"googlePalmApi": {"id": "DP6Uf0KoQgAhkgTD", "name": "bhagavd gita"}}}, {"parameters": {"content": "this is for only imagen3 google image generaton model, but first is gemeni 4 ulta\n", "color": 3}, "type": "n8n-nodes-base.stickyNote", "position": [-32, 3200], "typeVersion": 1, "id": "5b5b2689-586d-40de-abc3-c629a3097a62", "name": "<PERSON><PERSON>"}, {"parameters": {"documentId": {"__rl": true, "value": "1BUgqE4bq-F6fjSSsoYm7TO36TGRfZNr5s8MQy6vO21w", "mode": "list", "cachedResultName": "Bhagavad-Gita-details", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1BUgqE4bq-F6fjSSsoYm7TO36TGRfZNr5s8MQy6vO21w/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1BUgqE4bq-F6fjSSsoYm7TO36TGRfZNr5s8MQy6vO21w/edit#gid=0"}, "filtersUI": {"values": [{"lookupColumn": "status", "lookupValue": "="}]}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [-256, 1936], "id": "ece27d8a-0d86-411f-a34f-92d9ae904117", "name": "Get row(s) in sheet", "credentials": {"googleSheetsOAuth2Api": {"id": "0UuddpDVrXy82EX8", "name": "korn"}}}, {"parameters": {"operation": "update", "documentId": {"__rl": true, "value": "1BUgqE4bq-F6fjSSsoYm7TO36TGRfZNr5s8MQy6vO21w", "mode": "list", "cachedResultName": "Bhagavad-Gita-details", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1BUgqE4bq-F6fjSSsoYm7TO36TGRfZNr5s8MQy6vO21w/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1BUgqE4bq-F6fjSSsoYm7TO36TGRfZNr5s8MQy6vO21w/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"status": "=video is sucessfully uploaded to youtube and saved a raw copy in the local machine check it.", "Chapter-Verse": "={{ $('Read/Write Files from Disk29').item.json.fileName.match(/Chapter_(\\d+)_Verse_(\\d+)\\.mp4/).slice(1).join('-') }}"}, "matchingColumns": ["Chapter-Verse"], "schema": [{"id": "S.No", "displayName": "S.No", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": true}, {"id": "Chapter-Verse", "displayName": "Chapter-Verse", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Audio Link", "displayName": "Audio Link", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": true}, {"id": "Sanskrit Shloka", "displayName": "Sanskrit Shloka", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": true}, {"id": "Romanized Transliteration", "displayName": "Romanized Transliteration", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": true}, {"id": "\tEnglish Translation", "displayName": "\tEnglish Translation", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": true}, {"id": "Word-by-Word Translation", "displayName": "Word-by-Word Translation", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": true}, {"id": "image-link", "displayName": "image-link", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": true}, {"id": "thumnail-link", "displayName": "thumnail-link", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": true}, {"id": "video-link", "displayName": "video-link", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": true}, {"id": "status", "displayName": "status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "row_number", "displayName": "row_number", "required": false, "defaultMatch": false, "display": true, "type": "number", "canBeUsedToMatch": true, "readOnly": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [4272, 2496], "id": "cba30ffa-0ff8-439e-a991-3944b586ae52", "name": "Update row in sheet", "retryOnFail": true, "credentials": {"googleSheetsOAuth2Api": {"id": "0UuddpDVrXy82EX8", "name": "korn"}}}, {"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-672, 1664], "id": "f68f6e6c-4210-40a2-9a00-c08642a00e5c", "name": "When clicking ‘Execute workflow’"}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-tts:generateContent?key=AIzaSyCop09ScIqtX-h2MqefoDSuI71dkgr3J8I", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"contents\": [\n    {\n      \"parts\": [\n        {\n          \"text\": \"{{ $json.output }}\"\n        }\n      ]\n    }\n  ],\n  \"generationConfig\": {\n    \"responseModalities\": [\"AUDIO\"],\n    \"speechConfig\": {\n      \"voiceConfig\": {\n        \"prebuiltVoiceConfig\": {\n          \"voiceName\": \"Alnilam\"\n        }\n      }\n    }\n  },\n  \"model\": \"gemini-2.5-flash-preview-tts\"\n}\n", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [672, 1728], "id": "c64bf3cf-dd52-4de2-a725-2cfe3296806c", "name": "HTTP Request110", "executeOnce": false, "retryOnFail": true, "maxTries": 2}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/imagen-4.0-generate-preview-06-06:predict?key=AIzaSyARDGaCg7Ddw9jORagq4fA1N9WSbi4xT3I", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ \nJSON.stringify({\n  instances: [\n    {\n      prompt: \n        $json.output + \n        \" Embed a banner directly into the top part of the image, seamlessly integrated into the artwork. The banner must contain ONLY the text: Chapter \" +\n        $('Read/Write Files from Disk20').item.json.fileName.match(/Chapter_(\\d+)_Verse_\\d+/)?.[1] +\n        \", Verse \" +\n        $('Read/Write Files from Disk20').item.json.fileName.match(/Chapter_\\d+_Verse_(\\d+)/)?.[1] +\n        \". IMPORTANT: Ignore any other text content that may be present in the image prompt above. Do not include any additional text, labels, titles (such as 'Bhagavad Gita'), quotes, verses, descriptions, or any other textual content from the prompt - ONLY display the chapter and verse numbers as specified. Do not include any symbols, decorative script, or other text elements. Use Sanskrit-inspired calligraphy (in English letters) with golden or saffron text on a rich red or dark scroll-like background. Make sure the banner blends naturally into the image as a part of the visual composition, not a floating element or external frame.\"\n    }\n  ],\n  parameters: {\n    numberOfImages: 1,\n    aspectRatio: \"9:16\",\n    personGeneration: \"allow_all\"\n  }\n})\n}}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [336, 3344], "id": "4b94b240-c7ba-4d5f-bf44-58dcae7fcd1c", "name": "HTTP Request", "retryOnFail": true, "maxTries": 2}, {"parameters": {"promptType": "define", "text": "={{ $json.output }}", "hasOutputParser": true, "options": {"systemMessage": "You are an expert image prompt variation generator specializing in creating diverse yet cohesive visual interpretations of Bhagavad Gita content. Your task is to take an existing image generation prompt and create 6 unique variations that maintain the core essence while introducing subtle differences in perspective, composition, and visual elements.\n\n### Bhagavad Gita Knowledge Base\n**Historical Context:** Mahabharata era (3102 BCE), Kurukshetra battlefield, ancient Indian civilization\n**Core Characters:**\n- **Krishna:** Divine avatar, blue-skinned, peacock feather crown, yellow silk dhoti, flute or discus, serene expression\n- **A<PERSON><PERSON>a:** Warrior prince, fair-skinned, ornate armor, bow and arrows, conflicted expression\n- **Other Pandavas:** <PERSON><PERSON><PERSON><PERSON><PERSON> (eldest, righteous), <PERSON><PERSON> (strong, mace), <PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON> (twins)\n- **<PERSON><PERSON><PERSON>:** <PERSON><PERSON><PERSON><PERSON> (jealous prince), various warrior brothers\n- **Sages/Rishis:** Long beards, simple robes, meditation postures, peaceful aura\n\n**Divine Forms & Manifestations:**\n- **Vishwarupa:** <PERSON>'s cosmic form - multiple arms, divine weapons, celestial radiance\n- **Chaturbhuja:** Four-armed form holding conch, discus, mace, lotus\n- **Normal Human Form:** Two-armed <PERSON> as charioteer and friend\n\n**Cultural & Visual Elements:**\n- **Architecture:** Ancient Indian temples, pillared halls, ornate carvings, stone structures\n- **Clothing:** Silk dhotis, jewelry, crowns, armor, sacred threads\n- **Weapons:** Bows, arrows, swords, maces, discus (chakra), conch shells\n- **Vehicles:** Horse-drawn chariots, divine vimanas (aerial vehicles)\n- **Nature:** Banyan trees, lotus flowers, Yamuna river, pastoral landscapes\n- **Battlefield:** Kurukshetra - vast plain, armies, flags, dust clouds\n\n### Character Positioning & Consistency Guidelines\n**Character Duplication Prevention:**\n- Each character appears only ONCE per image\n- No multiple versions of the same character in single scene\n- Clear character identity distinction required\n\n**Positioning Specifications:**\n- **Primary Characters:** Foreground placement, clear visibility, proper proportions\n- **Secondary Characters:** Middle-ground or background, supporting roles\n- **Divine Forms:** Central, elevated, or dominant positioning\n- **Crowd Scenes:** Individual characters distinguishable, no generic duplicates\n\n**Expression & Interaction:**\n- **Krishna:** Calm, wise, compassionate, sometimes playful\n- **Arjuna:** Conflicted, respectful, seeking guidance, warrior-ready\n- **Dialogue Scenes:** Appropriate eye contact, body language, spatial relationships\n- **Battle Scenes:** Dynamic positioning, realistic combat stances\n\n### Input Processing\n**Receive:** A single image generation prompt for Bhagavad Gita content\n**Analyze:** Core elements - characters, setting, emotion, action, style, quality\n**Preserve:** Essential narrative, character identity, cultural authenticity, spiritual meaning\n**Modify:** Camera angles, positioning, lighting, atmospheric details, composition\n\n### Variation Strategy\n**Core Elements to Maintain:**\n- **Character Identity:** Exact appearance, clothing, divine attributes, unique features\n- **Character Count:** Same number of characters, no duplicates, clear individual identity\n- **Cultural Authenticity:** Mahabharata-era details, Indian architecture, traditional clothing\n- **Spiritual Essence:** Divine aura, emotional tone, sacred atmosphere\n- **Narrative Context:** Verse meaning, philosophical depth, character relationships\n- **Technical Quality:** Photorealistic, 4K HDR, cinematic composition\n\n**Character Consistency Requirements:**\n- **Krishna:** Blue skin, peacock feather crown, yellow silk dhoti, divine radiance\n- **Arjuna:** Fair skin, ornate Kshatriya armor, bow, princely bearing\n- **Facial Features:** Maintain consistent appearance across all variations\n- **Proportions:** Realistic human proportions, no distortion\n- **Positioning:** Clear spatial relationships, logical interactions\n\n**Elements to Vary:**\n- **Camera Angles:** Medium shot, wide shot, low angle, high angle, side profile, three-quarter view\n- **Character Positioning:** Left/right placement, foreground/background, facing direction\n- **Lighting Conditions:** Golden hour, dramatic shadows, soft morning light, backlit, side-lit\n- **Atmospheric Effects:** Mist levels, dust particles, cloud formations, wind intensity\n- **Composition:** Rule of thirds, centered, diagonal composition, symmetrical, asymmetrical\n- **Environmental Details:** Background elements, architectural features, natural phenomena\n\n### Technical Specifications for Imagen 3\n**Quality Parameters:**\n- **Resolution:** 4K HDR, ultra-high definition\n- **Style:** Photorealistic, cinematic quality\n- **Lighting:** Professional cinematography standards\n- **Details:** Rich textures, intricate patterns, realistic materials\n- **Color:** Vibrant but authentic, culturally appropriate palette\n\n### Variation Framework\n**Variation 1: Camera Perspective Change**\n- Modify viewing angle (high angle to low angle, or vice versa)\n- Adjust distance (medium shot to wide shot, or panoramic view)\n- Change character focal point while maintaining scene integrity\n- Preserve character details and positioning logic\n\n**Variation 2: Lighting Transformation**\n- Alter time of day or lighting source\n- Modify shadow direction and intensity\n- Change atmospheric lighting (golden hour to dramatic storm light)\n- Maintain character visibility and divine radiance\n\n**Variation 3: Character Positioning Shift**\n- Reposition characters within the frame (left to right, foreground to background)\n- Modify character orientation (facing direction, body angle)\n- Adjust character interactions and spatial relationships\n- Ensure no character duplication or identity confusion\n\n**Variation 4: Atmospheric Enhancement**\n- Intensify or reduce environmental effects (mist, dust, wind)\n- Modify weather conditions (clear sky to gathering clouds)\n- Adjust particle effects and atmospheric depth\n- Preserve character clarity and divine aura\n\n**Variation 5: Compositional Restructure**\n- Change framing (vertical emphasis to horizontal balance)\n- Modify background prominence (detailed to simplified, or vice versa)\n- Adjust visual weight distribution across the frame\n- Maintain character hierarchy and narrative focus\n\n**Variation 6: Environmental Detail Variation**\n- Modify background architectural elements\n- Adjust natural environment features (trees, rocks, water)\n- Change environmental props (chariots, weapons, banners)\n- Enhance cultural authenticity and period accuracy\n\n### Quality Consistency Rules\n**Character Specifications:**\n- **Maintain Exact Appearance:** Same facial features, body proportions, divine attributes\n- **Preserve Identity:** Krishna's blue skin, Arjuna's fair complexion, unique characteristics\n- **Consistent Clothing:** Traditional attire, jewelry, weapons, cultural accessories\n- **Expression Authenticity:** Appropriate emotions, spiritual depth, character personality\n\n**Technical Consistency:**\n- **Resolution:** 4K HDR, ultra-high definition across all variations\n- **Style:** Photorealistic, cinematic quality, professional cinematography\n- **Cultural Accuracy:** Mahabharata-era details, Indian architecture, authentic elements\n- **Spiritual Atmosphere:** Divine radiance, sacred ambiance, emotional resonance\n\n**Narrative Consistency:**\n- **Story Context:** Maintain verse meaning, philosophical essence, character relationships\n- **Temporal Consistency:** Same historical period, consistent environmental details\n- **Emotional Tone:** Preserve spiritual gravity, divine interaction, human emotion\n\n**Critical Prevention Rules:**\n- **No Character Duplication:** Each character appears only once per image\n- **No Identity Confusion:** Clear distinction between different characters\n- **No Anachronisms:** Maintain historical period accuracy\n- **No Cultural Misrepresentation:** Respect spiritual and cultural authenticity\n\n**Modification Guidelines:**\n- **Preserve Character Integrity:** No alteration of character identity, appearance, or count\n- **Maintain Spiritual Authenticity:** Respect divine forms, sacred interactions, cultural reverence\n- **Ensure Visual Distinction:** Each variation offers unique perspective while maintaining core essence\n- **Prevent Duplication:** No repeated characters, clear individual identity across all variations\n- **Enhance Narrative Depth:** Variations should enrich the story, not dilute its meaning\n- **Optimize for Imagen 3:** Generate prompts specifically formatted for Google's Imagen 3 model\n\n### Output Format Requirements\nGenerate exactly 6 variations in the following JSON structure:\n\n```json\n{\n  \"variations\": [\n    \"variation_1_prompt_here\",\n    \"variation_2_prompt_here\", \n    \"variation_3_prompt_here\",\n    \"variation_4_prompt_here\",\n    \"variation_5_prompt_here\",\n    \"variation_6_prompt_here\"\n  ]\n}\n```\n\n**Critical Output Parameter:**\n- **Single Line Format:** Each variation prompt must be formatted as a single continuous line without line breaks or paragraph separations\n- **Complete Descriptions:** Each prompt must be a complete, self-contained description (400+ characters minimum)\n- **Character Detail Rich:** Include specific character descriptions, positioning, interactions, and cultural elements\n- **Imagen 3 Optimized:** Prompts specifically formatted for Google's Imagen 3 image generation model\n- **No Fragmentation:** Avoid splitting descriptions across multiple lines or sentences that would break the prompt structure\n\n### Processing Instructions\n1. **Analyze Input:** Break down the original prompt into character details, positioning, lighting, atmosphere, and cultural elements\n2. **Identify Character Constants:** Note exact character count, appearance details, divine attributes, and positioning logic\n3. **Map Variation Strategy:** Plan specific modifications for each of the 6 variations while preserving character integrity\n4. **Enhance Character Details:** Ensure each variation includes comprehensive character descriptions, cultural authenticity, and spiritual essence\n5. **Generate Rich Prompts:** Create detailed prompts maintaining 400+ character richness with specific visual and cultural details\n6. **Validate Character Consistency:** Verify no character duplication, identity confusion, or cultural misrepresentation\n7. **Optimize for Imagen 3:** Ensure prompts are formatted and detailed for optimal Google Imagen 3 performance\n8. **Format Output:** Structure in the required JSON format with single-line prompts\n\n### Example Variation Approach\n**If Original Prompt Features:** \"Krishna with blue skin, peacock feather crown, yellow silk dhoti, standing center foreground speaking to Arjuna, fair-skinned warrior prince in ornate armor kneeling left background, golden hour lighting, Kurukshetra battlefield, photorealistic 4K HDR\"\n\n**Possible Variations:**\n1. **Camera Shift:** \"Krishna with blue skin, peacock feather crown, yellow silk dhoti positioned right foreground, low-angle perspective, Arjuna fair-skinned warrior prince in ornate armor standing left middle-ground, golden hour lighting, Kurukshetra battlefield, photorealistic 4K HDR\"\n2. **Lighting Change:** \"Krishna with blue skin, peacock feather crown, yellow silk dhoti center foreground, dramatic side-lighting with deep shadows, Arjuna fair-skinned warrior prince in ornate armor kneeling left background, storm clouds gathering, Kurukshetra battlefield, photorealistic 4K HDR\"\n3. **Positioning:** \"Krishna with blue skin, peacock feather crown, yellow silk dhoti and Arjuna fair-skinned warrior prince in ornate armor side by side, both standing, facing slightly toward each other, golden hour lighting, Kurukshetra battlefield, photorealistic 4K HDR\"\n4. **Atmospheric:** \"Krishna with blue skin, peacock feather crown, yellow silk dhoti center foreground, misty atmosphere with swirling battlefield dust, Arjuna fair-skinned warrior prince in ornate armor kneeling left background, golden hour lighting, Kurukshetra battlefield, photorealistic 4K HDR\"\n5. **Composition:** \"Wide shot with Krishna blue skin, peacock feather crown, yellow silk dhoti center, expansive Kurukshetra battlefield background, Arjuna fair-skinned warrior prince in ornate armor visible in right background, golden hour lighting, photorealistic 4K HDR\"\n6. **Environmental:** \"Krishna with blue skin, peacock feather crown, yellow silk dhoti center foreground, detailed royal chariot visible right side, Arjuna fair-skinned warrior prince in ornate armor kneeling behind chariot wheel, golden hour lighting, Kurukshetra battlefield, photorealistic 4K HDR\"\n\n🎯 **Your Task:** Analyze the provided image generation prompt and create 6 unique variations that will produce visually distinct but thematically consistent images for Google's Imagen 3 model, maintaining all character details, cultural authenticity, and spiritual depth while offering diverse visual perspectives and preventing character duplication."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [-736, 4864], "id": "233515c2-c3b4-483f-bcdb-ea154ea28789", "name": "AI Agent", "retryOnFail": true, "maxTries": 2}, {"parameters": {"modelName": "models/gemini-2.5-pro", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [-784, 5360], "id": "657fe7b4-4c58-4db6-87bd-b57824345504", "name": "Google Gemini Chat Model", "credentials": {"googlePalmApi": {"id": "c25VTQYCd5Of6Byf", "name": "earn money te;u"}}}, {"parameters": {"jsonSchemaExample": "{\n  \"variations\": [\n    \"variation_1_prompt_here\",\n    \"variation_2_prompt_here\", \n    \"variation_3_prompt_here\",\n    \"variation_4_prompt_here\",\n    \"variation_5_prompt_here\",\n    \"variation_6_prompt_here\"\n  ]\n}", "autoFix": true}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.3, "position": [-608, 5184], "id": "89f231f2-4e12-4b42-b24c-86197e69f709", "name": "Structured Output Parser1"}, {"parameters": {"resource": "video", "operation": "upload", "title": "=భగవద్గీత {{ $('Process One Verse at a Time2').item.json['Chapter-Verse'].replace('-', '.') }}:{{ $('AI Agent6').item.json.output.title }} #bhagavadgita #shorts", "regionCode": "IN", "categoryId": "27", "options": {"defaultLanguage": "te", "description": "={{ $('AI Agent6').item.json.output.description }}\n\nChapter-Verse: {{ $('Process One Verse at a Time2').item.json['Chapter-Verse'] }}\n\n{{ $('Process One Verse at a Time2').item.json['Sanskrit Shloka'] }}\n\n{{ $('Process One Verse at a Time2').item.json['Romanized Transliteration'] }}\nEnglish Translation:\n{{ $('Process One Verse at a Time2').item.json['\tEnglish Translation'] }}\nWord-by-Word Translation:\n{{ $('Process One Verse at a Time2').item.json['Word-by-Word Translation'] }}\n\n🔔 Subscribe for daily Gita wisdom: https://www.youtube.com/@Gita_Gyanaam\n📸 Instagram: https://www.instagram.com/gita_gyanaam/\n📘 Facebook: https://www.facebook.com/profile.php?id=61577900636828\n💌 Donations & Contact: <EMAIL>\n\n🎵 Track Info:\nMusic: Devotional - Deepak Meenu  \nMusic Link: https://royaltyfreebgmusic.blogspot.com\n\n📜 Disclaimer:\nI do not own the rights to this music. All credits go to the original artist, composer, and producer.  \nThis music is used under the terms stated by the uploader in the video linked above.\n\n\n{{ $('AI Agent6').item.json.output.searchSummary }}\n\n📚 Discover more Bhagavad Gita wisdom in Telugu, English, and other Indian languages — including full chapters (like Chapter 1, 2, 12), meaningful verses (e.g., 2.47), Sanskrit + English translations, Chaganti & Ghantasala slokas, and spiritual songs.\n\n{{ $('AI Agent6').item.json.output.trendingHashtags }}", "license": "youtube", "privacyStatus": "private", "selfDeclaredMadeForKids": false, "tags": "={{ $('AI Agent6').item.json.output.trendingHashtags }}"}}, "type": "n8n-nodes-base.youTube", "typeVersion": 1, "position": [3936, 2848], "id": "8677383f-bde1-4449-b2c6-77a30ab17a10", "name": "Upload a video", "alwaysOutputData": true, "credentials": {"youTubeOAuth2Api": {"id": "V0poigWaAdu7vVo7", "name": "bhagavd gita"}}}, {"parameters": {"rule": {"interval": [{"field": "minutes", "minutesInterval": 15}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [-672, 1952], "id": "74d17351-b0bb-4cd4-8389-44152b1353f1", "name": "Schedule Trigger"}, {"parameters": {"operation": "write", "fileName": "=/shared-data/media/tts-text-for-futher-for-one-video/Chapter_{{ $('Process One Verse at a Time2').item.json['Chapter-Verse'].split('-')[0] }}_Verse_{{ $('Process One Verse at a Time2').item.json['Chapter-Verse'].split('-')[1] }}.txt", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [944, 2336], "id": "2d26e0b9-bca3-44ea-a92e-ee14fced32dc", "name": "Read/Write Files from Disk"}, {"parameters": {"operation": "toText", "sourceProperty": "output", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [656, 2336], "id": "3f181dc0-ee52-47c3-88fc-4758f8fea56b", "name": "Convert to File"}], "connections": {"Code9": {"main": [[{"node": "Read/Write Files from Disk19", "type": "main", "index": 0}]]}, "HTTP Request30": {"main": [[{"node": "Read/Write Files from Disk20", "type": "main", "index": 0}]]}, "Read/Write Files from Disk19": {"main": [[{"node": "HTTP Request30", "type": "main", "index": 0}]]}, "AI Agent5": {"main": [[{"node": "Convert to File", "type": "main", "index": 0}, {"node": "HTTP Request110", "type": "main", "index": 0}]]}, "Google Gemini Chat Model5": {"ai_languageModel": [[{"node": "AI Agent5", "type": "ai_languageModel", "index": 0}]]}, "Process One Verse at a Time2": {"main": [[], [{"node": "AI Agent5", "type": "main", "index": 0}]]}, "Read/Write Files from Disk20": {"main": [[{"node": "HTTP Request31", "type": "main", "index": 0}]]}, "HTTP Request31": {"main": [[{"node": "Code10", "type": "main", "index": 0}]]}, "Code10": {"main": [[{"node": "Save ass2", "type": "main", "index": 0}]]}, "Save ass2": {"main": [[{"node": "AI Agent9", "type": "main", "index": 0}]]}, "Create Video1": {"main": [[{"node": "AI Agent6", "type": "main", "index": 0}]]}, "AI Agent6": {"main": [[{"node": "Read/Write Files from Disk29", "type": "main", "index": 0}]]}, "Google Gemini Chat Model6": {"ai_languageModel": [[{"node": "AI Agent6", "type": "ai_languageModel", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "AI Agent6", "type": "ai_outputParser", "index": 0}]]}, "Read/Write Files from Disk29": {"main": [[{"node": "Upload a video", "type": "main", "index": 0}]]}, "Read/Write Files from Disk30": {"main": [[{"node": "HTTP Request41", "type": "main", "index": 0}]]}, "Read/Write Files from Disk39": {"main": [[{"node": "Merge4", "type": "main", "index": 0}]]}, "Read/Write Files from Disk40": {"main": [[{"node": "Merge4", "type": "main", "index": 2}]]}, "Read/Write Files from Disk41": {"main": [[{"node": "Merge4", "type": "main", "index": 3}]]}, "Read/Write Files from Disk42": {"main": [[{"node": "Merge4", "type": "main", "index": 6}]]}, "Read/Write Files from Disk43": {"main": [[{"node": "Merge4", "type": "main", "index": 7}]]}, "Read/Write Files from Disk44": {"main": [[{"node": "Merge4", "type": "main", "index": 4}]]}, "Read/Write Files from Disk45": {"main": [[{"node": "Merge4", "type": "main", "index": 5}]]}, "Read/Write Files from Disk46": {"main": [[{"node": "Merge4", "type": "main", "index": 1}]]}, "Merge4": {"main": [[{"node": "Create Video1", "type": "main", "index": 0}]]}, "Convert to File8": {"main": [[{"node": "Read/Write Files from Disk39", "type": "main", "index": 0}]]}, "HTTP Request46": {"main": [[{"node": "Convert to File9", "type": "main", "index": 0}]]}, "Convert to File9": {"main": [[{"node": "Read/Write Files from Disk40", "type": "main", "index": 0}]]}, "HTTP Request62": {"main": [[{"node": "Convert to File10", "type": "main", "index": 0}]]}, "Convert to File10": {"main": [[{"node": "Read/Write Files from Disk42", "type": "main", "index": 0}]]}, "HTTP Request63": {"main": [[{"node": "Convert to File11", "type": "main", "index": 0}]]}, "Convert to File11": {"main": [[{"node": "Read/Write Files from Disk43", "type": "main", "index": 0}]]}, "HTTP Request64": {"main": [[{"node": "Convert to File20", "type": "main", "index": 0}]]}, "Convert to File20": {"main": [[{"node": "Read/Write Files from Disk46", "type": "main", "index": 0}]]}, "HTTP Request65": {"main": [[{"node": "Convert to File21", "type": "main", "index": 0}]]}, "Convert to File21": {"main": [[{"node": "Read/Write Files from Disk44", "type": "main", "index": 0}]]}, "HTTP Request66": {"main": [[{"node": "Convert to File22", "type": "main", "index": 0}]]}, "Convert to File22": {"main": [[{"node": "Read/Write Files from Disk45", "type": "main", "index": 0}]]}, "HTTP Request67": {"main": [[{"node": "Convert to File23", "type": "main", "index": 0}]]}, "Convert to File23": {"main": [[{"node": "Read/Write Files from Disk41", "type": "main", "index": 0}]]}, "AI Agent9": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}, {"node": "HTTP Request", "type": "main", "index": 0}, {"node": "HTTP Request64", "type": "main", "index": 0}]]}, "Google Gemini Chat Model9": {"ai_languageModel": [[{"node": "AI Agent9", "type": "ai_languageModel", "index": 0}]]}, "Get row(s) in sheet": {"main": [[{"node": "Process One Verse at a Time2", "type": "main", "index": 0}]]}, "HTTP Request110": {"main": [[{"node": "Code9", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Convert to File8", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "HTTP Request46", "type": "main", "index": 0}, {"node": "HTTP Request67", "type": "main", "index": 0}, {"node": "HTTP Request65", "type": "main", "index": 0}, {"node": "HTTP Request66", "type": "main", "index": 0}, {"node": "HTTP Request62", "type": "main", "index": 0}, {"node": "HTTP Request63", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}, {"node": "Structured Output Parser1", "type": "ai_languageModel", "index": 0}]]}, "Structured Output Parser1": {"ai_outputParser": [[{"node": "AI Agent", "type": "ai_outputParser", "index": 0}]]}, "Upload a video": {"main": [[{"node": "Update row in sheet", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "Get row(s) in sheet", "type": "main", "index": 0}]]}, "Convert to File": {"main": [[{"node": "Read/Write Files from Disk", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "5d4869003d307ea5add41a6c7754865a420a7910dc8e17f8b2208a40aa5373c6"}}