# Ignore shared-data subdirectories
shared-data/ass-scripts/
shared-data/downloads/
shared-data/images/
shared-data/videos/
shared-data/temp/
shared-data/media/tts-text-for-futher-for-one-video

# Ignore n8n data directory
n8n-data/

# Optional: Add common ignore patterns
.DS_Store
*.log
node_modules/
.env
.env.local
.env.production
.env.staging

# Python cache and virtual environments
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
venv/
env/
ENV/

# Media processing temporary files
*.tmp
*.temp
*.cache
*.ffmpeg
*.processing

# Docker and container files
*.pid
*.seed
*.pid.lock

# IDE and editor files
.vscode/settings.json
.vscode/launch.json
.idea/
*.swp
*.swo
*~

# Backup files
*.bak
*.backup
*.old

# Large media files (if any accidentally get added)
*.mov
*.avi
*.mkv
*.flv
*.wmv
