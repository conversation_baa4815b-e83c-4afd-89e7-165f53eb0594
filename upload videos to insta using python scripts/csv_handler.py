import csv
import os
from typing import Dict, Optional, Tuple
from logger import log_info, log_error, log_warning

class BhagavadGitaCSVHandler:
    """Handle Bhagavad Gita verse data from CSV file."""
    
    def __init__(self, csv_file_path='Bhagavad-Gita-details - Sheet1.csv'):
        self.csv_file_path = csv_file_path
        self.verses_data = {}
        self._load_csv_data()
    
    def _load_csv_data(self):
        """Load all verse data from CSV file into memory."""
        try:
            if not os.path.exists(self.csv_file_path):
                log_error(f"CSV file not found: {self.csv_file_path}")
                return
            
            with open(self.csv_file_path, 'r', encoding='utf-8') as file:
                # Use csv.reader to handle proper CSV parsing
                csv_reader = csv.reader(file)
                headers = next(csv_reader)  # Skip header row
                
                for row in csv_reader:
                    if len(row) >= 7:  # Ensure we have all required columns
                        try:
                            # Extract data from CSV columns
                            s_no = row[0]
                            chapter_verse = row[1]  # Format: "2-8"
                            audio_link = row[2]
                            sanskrit_shloka = row[3]
                            romanized_transliteration = row[4]
                            english_translation = row[5]
                            word_by_word_translation = row[6]
                            
                            # Parse chapter-verse
                            if '-' in chapter_verse:
                                chapter, verse = chapter_verse.split('-')
                                chapter = int(chapter)
                                verse = int(verse)
                                
                                # Store the data
                                self.verses_data[(chapter, verse)] = {
                                    's_no': s_no,
                                    'chapter_verse': chapter_verse,
                                    'audio_link': audio_link,
                                    'sanskrit_shloka': sanskrit_shloka,
                                    'romanized_transliteration': romanized_transliteration,
                                    'english_translation': english_translation,
                                    'word_by_word_translation': word_by_word_translation
                                }
                        except (ValueError, IndexError) as e:
                            log_warning(f"Error parsing row {s_no}: {e}")
                            continue
            
            log_info(f"Loaded {len(self.verses_data)} verses from CSV file")
            
        except Exception as e:
            log_error(f"Error loading CSV data: {e}")
    
    def get_verse_data(self, chapter: int, verse: int) -> Optional[Dict]:
        """Get verse data for specific chapter and verse."""
        try:
            verse_data = self.verses_data.get((chapter, verse))
            if verse_data:
                log_info(f"Found CSV data for Chapter {chapter}, Verse {verse}")
                return verse_data
            else:
                log_warning(f"No CSV data found for Chapter {chapter}, Verse {verse}")
                return None
        except Exception as e:
            log_error(f"Error retrieving verse data for {chapter}-{verse}: {e}")
            return None
    
    def format_verse_data_for_caption(self, chapter: int, verse: int) -> Optional[Dict]:
        """Format verse data for caption generation."""
        try:
            verse_data = self.get_verse_data(chapter, verse)
            if not verse_data:
                return None
            
            # Clean and format the data
            formatted_data = {
                'chapter': chapter,
                'verse': verse,
                'chapter_verse': verse_data['chapter_verse'],
                'sanskrit': self._clean_sanskrit(verse_data['sanskrit_shloka']),
                'transliteration': self._clean_transliteration(verse_data['romanized_transliteration']),
                'translation': self._clean_translation(verse_data['english_translation']),
                'word_meanings': self._clean_word_meanings(verse_data['word_by_word_translation'])
            }
            
            log_info(f"Formatted verse data for Chapter {chapter}, Verse {verse}")
            return formatted_data
            
        except Exception as e:
            log_error(f"Error formatting verse data for {chapter}-{verse}: {e}")
            return None
    
    def _clean_sanskrit(self, sanskrit_text: str) -> str:
        """Clean and format Sanskrit text."""
        try:
            # Remove extra whitespace and formatting
            cleaned = sanskrit_text.strip()
            # Remove verse number if present at the end
            if cleaned.endswith(f" {self.verses_data.get('chapter_verse', '')}"):
                cleaned = cleaned.replace(f" {self.verses_data.get('chapter_verse', '')}", "")
            return cleaned
        except:
            return sanskrit_text
    
    def _clean_transliteration(self, transliteration_text: str) -> str:
        """Clean and format transliteration text."""
        try:
            # Remove extra whitespace
            cleaned = transliteration_text.strip()
            
            # Remove chapter-verse reference at the end if present
            # Look for patterns like "2-2", "1-1", etc. at the end
            import re
            # Remove any chapter-verse pattern at the end
            cleaned = re.sub(r'\s+\d+-\d+\s*$', '', cleaned)
            
            # Also remove if it's at the end of the last line
            lines = cleaned.split('\n')
            if lines:
                last_line = lines[-1].strip()
                # Check if last line ends with chapter-verse pattern
                last_line = re.sub(r'\s+\d+-\d+\s*$', '', last_line)
                lines[-1] = last_line
                cleaned = '\n'.join(lines)
            
            return cleaned.strip()
        except Exception as e:
            log_warning(f"Error cleaning transliteration: {e}")
            return transliteration_text
    
    def _clean_translation(self, translation_text: str) -> str:
        """Clean and format English translation."""
        try:
            return translation_text.strip()
        except:
            return translation_text
    
    def _clean_word_meanings(self, word_meanings_text: str) -> str:
        """Clean and format word-by-word meanings."""
        try:
            return word_meanings_text.strip()
        except:
            return word_meanings_text
    
    def _clean_word_meanings(self, word_meanings_text: str) -> str:
        """Clean and format word-by-word meanings."""
        try:
            # Remove trailing semicolons and clean up
            cleaned = word_meanings_text.strip().rstrip(';')
            return cleaned
        except:
            return word_meanings_text
    
    def get_available_verses(self) -> list:
        """Get list of all available verses in the CSV."""
        return list(self.verses_data.keys())
    
    def verify_verse_exists(self, chapter: int, verse: int) -> bool:
        """Check if a specific verse exists in the CSV data."""
        return (chapter, verse) in self.verses_data
