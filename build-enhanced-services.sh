#!/bin/bash

# Enhanced Build Script for 9:16 Video Generation with ASS Subtitles
# This script builds and deploys the upgraded video generation services

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${PURPLE}[ENHANCED BUILD]${NC} $1"
}

# Main execution
main() {
    print_header "🚀 Building Enhanced Video Generation Services"
    echo "=============================================="
    
    print_status "📱 Features: 9:16 Videos + ASS Subtitles + Karaoke Highlighting"
    echo ""
    
    # Check if Docker is running  
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    
    # Stop existing services
    print_status "🛑 Stopping existing services..."
    docker-compose down --volumes --remove-orphans > /dev/null 2>&1 || true
    
    # Clean up Docker environment
    print_status "🧹 Cleaning up Docker environment..."
    docker system prune -f > /dev/null 2>&1 || true
    
    print_status "📁 Ensure shared-data directories exist..."
    mkdir -p shared-data/{downloads,videos,images,temp,ass-scripts}
    
    # Build enhanced services
    print_status "🏗️ Building enhanced video service..."
    if docker-compose build --no-cache video-service; then
        print_success "✅ Enhanced video service built successfully"
    else
        print_error "❌ Enhanced video service build failed"
        exit 1
    fi
    
    # Start services
    print_status "🚀 Starting enhanced services..."
    if docker-compose up -d; then
        print_success "✅ Services started successfully"
    else
        print_error "❌ Failed to start services"
        exit 1
    fi
    
    # Wait for services to initialize
    print_status "⏳ Waiting for services to initialize..."
    sleep 15
    
    # Health checks
    print_status "🔍 Performing health checks..."
    
    # Check TTS Service
    if curl -s http://localhost:3001/health > /dev/null; then
        print_success "✅ TTS Service: Healthy"
    else
        print_warning "⚠️ TTS Service: Not responding (may still be starting)"
    fi
    
    # Check Enhanced Video Service
    if curl -s http://localhost:3002/health > /dev/null; then
        print_success "✅ Enhanced Video Service: Healthy"
        
        # Get service info
        SERVICE_INFO=$(curl -s http://localhost:3002/health | jq -r '.features[]' 2>/dev/null || echo "Features not available")
        echo ""
        print_status "📋 Enhanced Video Service Features:"
        echo "   • 9:16 Vertical Video Support"
        echo "   • ASS Subtitle Integration"  
        echo "   • Karaoke-style Text Highlighting"
        echo "   • Auto Audio Duration Detection"
        echo "   • Flexible Background Image Support"
        echo "   • Mobile-optimized Layout"
        echo "   • High-quality Telugu Font Support"
    else
        print_warning "⚠️ Enhanced Video Service: Not responding (may still be starting)"
    fi
    
    # Check n8n
    if curl -s http://localhost:5678/healthz > /dev/null; then
        print_success "✅ n8n: Healthy"
    else
        print_warning "⚠️ n8n: Not responding (may still be starting)"
    fi
    
    echo ""
    print_success "🎉 Enhanced Video Generation Stack is Ready!"
    echo ""
    echo "📱 Service URLs:"
    echo "   • n8n Interface: http://localhost:5678"
    echo "   • TTS Service: http://localhost:3001"  
    echo "   • Enhanced Video Service: http://localhost:3002"
    echo ""
    echo "🎬 New Features:"
    echo "   • 9:16 Aspect Ratio (1080x1920) - Perfect for mobile!"
    echo "   • ASS Subtitle Support with Karaoke Highlighting"
    echo "   • Auto Audio Duration Detection (+ 5 seconds extra)"
    echo "   • Simple Background Image Selection by Filename"
    echo "   • Improved Telugu Font Rendering"
    echo "   • Optimized for Instagram/YouTube Shorts/TikTok"
    echo ""
    echo "📁 File Directories:"
    echo "   • Audio files: ./shared-data/downloads/"
    echo "   • Video files: ./shared-data/videos/"
    echo "   • Your background images: ./shared-data/images/ (place your 9:16 images here)"
    echo "   • ASS subtitle files: ./shared-data/ass-scripts/"
    echo ""
    echo "🔧 Useful Commands:"
    echo "   • View logs: docker-compose logs -f"
    echo "   • Stop services: docker-compose down"
    echo "   • Restart: docker-compose restart"
    echo "   • Health check: curl http://localhost:3002/health"
    echo ""
    
    # Show container status
    print_status "📊 Container Status:"
    docker-compose ps
    
    echo ""
    print_header "🎯 Next Steps:"
    echo "1. Place your 9:16 background images in ./shared-data/images/ directory"
    echo "2. Update your n8n workflow with the new video creation endpoint"
    echo "3. Use 'create-enhanced-video' endpoint instead of 'create-video'"
    echo "4. Test with a single verse to verify 9:16 video generation"
    echo "5. Check the generated videos in ./shared-data/videos/ directory"
    echo ""
    print_success "Ready to create stunning 9:16 Bhagavad Gita videos! 🕉️"
}

# Handle script arguments
case "${1:-}" in
    "video-only")
        print_header "Building enhanced video service only..."
        docker-compose build --no-cache video-service
        docker-compose up -d video-service
        ;;
    "backgrounds-only")
        print_header "Creating shared-data directories for your files..."
        mkdir -p shared-data/{downloads,videos,images,temp,ass-scripts}
        echo "✅ Shared data directories ready. Place your files in ./shared-data/"
        ;;
    "health-check")
        print_header "Performing health checks..."
        echo "TTS Service:"
        curl -s http://localhost:3001/health | jq . || echo "Not available"
        echo ""
        echo "Enhanced Video Service:"
        curl -s http://localhost:3002/health | jq . || echo "Not available"
        ;;
    *)
        main
        ;;
esac
