import os
import requests
from pathlib import Path
from typing import Optional, Dict, Any
from logger import log_info, log_error
from config import CONFIG

class InstagramReelsUploader:
    """Class to handle Instagram Reels upload via Facebook Graph API"""
    
    def __init__(self, page_token: str, ig_business_id: str, api_version: str = 'v22.0'):
        self.page_token = page_token
        self.ig_business_id = ig_business_id
        self.api_version = api_version
        self.base_url = f'https://graph.facebook.com/{api_version}'
        
    def validate_config(self) -> bool:
        """Validate configuration parameters"""
        if not self.page_token or 'YOUR_PAGE_TOKEN' in self.page_token:
            log_error("Please set your PAGE_TOKEN in the configuration")
            return False
            
        if not self.ig_business_id or 'YOUR_IG_BUSINESS_ID' in self.ig_business_id:
            log_error("Please set your IG_BUSINESS_ID in the configuration")
            return False
            
        return True
    
    def validate_video_file(self, video_path: str) -> bool:
        """Validate video file exists and meets requirements"""
        if not os.path.exists(video_path):
            log_error(f"Video file not found: {video_path}")
            return False
            
        file_path = Path(video_path)
        
        if file_path.suffix.lower() not in CONFIG['SUPPORTED_FORMATS']:
            log_error(f"Unsupported file format. Supported: {CONFIG['SUPPORTED_FORMATS']}")
            return False
            
        file_size_mb = os.path.getsize(video_path) / (1024 * 1024)
        if file_size_mb > CONFIG['MAX_FILE_SIZE_MB']:
            log_error(f"File too large ({file_size_mb:.1f}MB). Max: {CONFIG['MAX_FILE_SIZE_MB']}MB")
            return False
            
        log_info(f"Video file validated: {file_path.name} ({file_size_mb:.1f}MB)")
        return True
    
    def create_media_container(self, caption: str) -> Optional[Dict[str, Any]]:
        """Step 1: Create a resumable media container"""
        log_info("Creating media container...")
        
        url = f"{self.base_url}/{self.ig_business_id}/media"
        data = {
            'media_type': 'REELS',
            'upload_type': 'resumable',
            'caption': caption,
            'access_token': self.page_token
        }
        
        try:
            response = requests.post(url, data=data)
            response.raise_for_status()
            
            result = response.json()
            container_id = result.get('id')
            upload_url = result.get('upload_session') or result.get('uri')
            
            if not container_id or not upload_url:
                log_error(f"Invalid response from container creation: {result}")
                return None
                
            log_info(f"Container created successfully! Container ID: {container_id}")
            return {'container_id': container_id, 'upload_url': upload_url}
            
        except requests.exceptions.RequestException as e:
            log_error(f"Error creating container: {e}")
            if hasattr(e, 'response') and e.response is not None:
                try:
                    error_details = e.response.json()
                    log_error(f"Error details: {error_details}")
                except:
                    log_error(f"HTTP Status: {e.response.status_code}")
            return None
    
    def upload_video_file(self, video_path: str, upload_url: str) -> bool:
        """Step 2: Upload the video file using resumable upload"""
        log_info("Uploading video file...")
        
        file_size = os.path.getsize(video_path)
        headers = {
            'Authorization': f'OAuth {self.page_token}',
            'offset': '0',
            'file_size': str(file_size),
            'Content-Type': 'application/octet-stream'
        }
        
        try:
            with open(video_path, 'rb') as video_file:
                response = requests.post(upload_url, headers=headers, data=video_file)
                response.raise_for_status()
                log_info(f"Video uploaded successfully! Status: {response.status_code}")
                return True
                
        except requests.exceptions.RequestException as e:
            log_error(f"Error uploading video: {e}")
            return False
        except IOError as e:
            log_error(f"Error reading video file: {e}")
            return False
    
    def publish_reel(self, container_id: str) -> Optional[str]:
        """Step 3: Publish the uploaded reel"""
        log_info("Publishing reel...")
        
        url = f"{self.base_url}/{self.ig_business_id}/media_publish"
        data = {
            'creation_id': container_id,
            'access_token': self.page_token
        }
        
        try:
            response = requests.post(url, data=data)
            response.raise_for_status()
            
            result = response.json()
            media_id = result.get('id')
            
            if media_id:
                log_info(f"Reel published successfully! Media ID: {media_id}")
                return media_id
            else:
                log_error(f"No media ID in publish response: {result}")
                return None
                
        except requests.exceptions.RequestException as e:
            log_error(f"Error publishing reel: {e}")
            return None
    
    def upload_reel(self, video_path: str, caption: str) -> Optional[str]:
        """Complete workflow to upload a reel with detailed error reporting"""
        log_info(f"Starting Instagram Reel upload for: {os.path.basename(video_path)}")
        
        try:
            if not self.validate_config():
                log_error("❌ Configuration validation failed")
                return None
                
            if not self.validate_video_file(video_path):
                log_error("❌ Video file validation failed")
                return None

            # Step 1: Create media container
            container_info = self.create_media_container(caption)
            if not container_info:
                log_error("❌ Failed to create media container")
                return None
                
            # Step 2: Upload video file
            upload_success = self.upload_video_file(video_path, container_info['upload_url'])
            if not upload_success:
                log_error("❌ Failed to upload video file")
                return None
                
            # Step 3: Publish reel
            media_id = self.publish_reel(container_info['container_id'])
            if not media_id:
                log_error("❌ Failed to publish reel")
                return None
                
            log_info(f"✅ Successfully uploaded reel with Media ID: {media_id}")
            return media_id
            
        except Exception as e:
            log_error(f"❌ Unexpected error in upload_reel: {e}")
            return None
