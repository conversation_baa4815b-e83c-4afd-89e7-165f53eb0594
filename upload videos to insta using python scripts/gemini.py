import google.generativeai as genai
from logger import log_info, log_error, log_warning
from csv_handler import BhagavadGitaCSVHandler

def configure_gemini(api_key):
    """Configure the Gemini API with the provided key."""
    try:
        genai.configure(api_key=api_key)
        log_info("Google Gemini API configured successfully.")
        return True
    except Exception as e:
        log_error(f"Failed to configure Gemini API: {e}")
        return False

def generate_caption_and_tags(chapter, verse):
    """Generate unified Instagram caption using CSV data for accuracy."""
    try:
        # Initialize CSV handler
        csv_handler = BhagavadGitaCSVHandler()
        
        # Get verse data from CSV
        verse_data = csv_handler.format_verse_data_for_caption(chapter, verse)
        
        if not verse_data:
            log_warning(f"No CSV data found for Chapter {chapter}, Verse {verse}. Using AI fallback.")
            return fallback_caption_generation(chapter, verse)
        
        # Use AI only for Telugu title and hashtags, CSV for accuracy
        model = genai.GenerativeModel('gemini-2.5-pro')
        
        # Enhanced AI prompt based on successful YouTube system
        ai_prompt = f"""You are an expert Instagram Reels metadata generator specialized in Telugu spiritual content, specifically Bhagavad Gita verses.

Your task is to analyze the provided Bhagavad Gita verse details and return a JSON object with optimized metadata for Instagram Reels algorithm.

**Bhagavad Gita Verse Details:**
Chapter-Verse: {chapter}-{verse}
Sanskrit Sloka: {verse_data['sanskrit']}
Romanized Transliteration: {verse_data['transliteration']}
English Translation: {verse_data['translation']}
Word-by-Word Translation: {verse_data['word_meanings']}

## TITLE REQUIREMENTS:
- Generate a short, emotionally engaging Telugu title that captures the verse essence
- Sound like natural spoken Telugu (avoid overly literary or formal phrasing)
- Do NOT use punctuation-heavy titles or generic lines like "శ్లోక వివరణ"
- Keep the title under 50 characters
- Examples: "కర్మ చేయండి ఫలితం ఆశించవద్దు", "సర్వధర్మాన్ పరిత్యజ్య", "మనస్సును నియంత్రించండి"

## HOOK REQUIREMENTS:
- Create a compelling hook in both Telugu and English on separate lines
- Format: "[Relevant emoji] [Telugu hook line about the verse's power/wisdom/impact]\\n[English hook line about the meaning/benefit] [Relevant emoji]"
- Choose emojis that match the verse's core teaching (⚔️ for duty, 🕉️ for wisdom, 🎯 for karma, 💪 for strength, 🌟 for divine knowledge, 🧘 for meditation, ❤️ for devotion)
- The Telugu and English hook lines together must not exceed 125 characters
- Make it emotionally engaging and curiosity-inducing

## SEARCH SUMMARY:
- Generate one natural, keyword-rich sentence using common search terms related to the verse content
- Include terms like "karma," "dharma," "surrender," "soul," "Krishna's teaching," etc.
- Write in simple English, readable and meaningful
- Limit to 200 characters max

## HASHTAGS:
- Generate 5-7 highly relevant hashtags based on the verse's core teaching
- Focus on spiritual, motivational, and philosophical themes
- Mix emotion-driven, action-based, and value-centered hashtags
- All hashtags must be one word only, lowercase, and relevant
- Format as space-separated string with # prefix

## OUTPUT FORMAT:
Return ONLY a valid JSON object with these exact fields:
- title: Short Telugu title only
- hook: Telugu + English hook with emojis
- searchSummary: Natural keyword-rich sentence
- hashtags: Space-separated hashtags string

Make content viral and engaging for Telugu spiritual audience focused on Instagram Reels."""
        
        log_info(f"Generating enhanced metadata for Chapter {chapter}, Verse {verse}")
        ai_response = model.generate_content(ai_prompt)
        ai_text = ai_response.text.strip()
        
        # Parse JSON response or fallback to defaults
        telugu_title = "తెలుగు శీర్షిక"  # Default fallback
        hook = "🕉️ భగవద్గీత నుండి దైవిక జ్ఞానం\nWisdom from the Bhagavad Gita 🌸"  # Default fallback
        search_summary = ""  # Will use algorithmic generation if AI fails
        hashtags = "#spiritualwisdom #bhagavadgita"  # Default fallback
        
        try:
            # Try to parse JSON response
            import json
            # Clean the response to extract JSON
            json_start = ai_text.find('{')
            json_end = ai_text.rfind('}') + 1
            if json_start >= 0 and json_end > json_start:
                json_text = ai_text[json_start:json_end]
                ai_data = json.loads(json_text)
                
                telugu_title = ai_data.get('title', telugu_title)
                hook = ai_data.get('hook', hook)
                search_summary = ai_data.get('searchSummary', '')
                hashtags = ai_data.get('hashtags', hashtags)
                
                log_info(f"Successfully parsed AI response for Chapter {chapter}, Verse {verse}")
            else:
                log_warning(f"No valid JSON found in AI response, using defaults")
                
        except (json.JSONDecodeError, KeyError) as e:
            log_warning(f"Error parsing AI JSON response, using defaults: {e}")
            # Fallback to old parsing method
            try:
                lines = ai_text.split('\n')
                for line in lines:
                    if line.startswith('TELUGU_TITLE:'):
                        telugu_title = line.replace('TELUGU_TITLE:', '').strip()
                    elif line.startswith('HASHTAGS:'):
                        hashtags = line.replace('HASHTAGS:', '').strip()
            except Exception as e2:
                log_warning(f"Fallback parsing also failed: {e2}")
        
        # Generate search summary algorithmically if not provided by AI
        if not search_summary:
            search_summary = _generate_search_summary(verse_data['translation'])
        
        # Generate complete caption using CSV data + enhanced AI content
        caption = f"""భగవద్గీత {chapter}.{verse}: {telugu_title}

{hook}

Chapter-Verse: {chapter}-{verse}

{verse_data['sanskrit']}

{verse_data['transliteration']}

English Translation:
{verse_data['translation']}

Word-by-Word Translation:
{verse_data['word_meanings']}

{search_summary}

📚 Discover more Bhagavad Gita wisdom in Telugu, English, and other Indian languages — including full chapters (like Chapter 1, 2, 12), meaningful verses (e.g., 2.47), Sanskrit + English translations, Chaganti & Ghantasala slokas, and spiritual songs.

{hashtags}"""
        
        log_info(f"Successfully generated caption using CSV data for Chapter {chapter}, Verse {verse}")
        log_info(f"Caption length: {len(caption)} characters")
        
        # Log preview
        preview = caption[:100] + "..." if len(caption) > 100 else caption
        log_info(f"Caption preview: {preview}")
        
        return caption
        
    except Exception as e:
        log_error(f"Error generating caption with CSV data: {e}")
        return fallback_caption_generation(chapter, verse)

def _generate_search_summary(translation: str) -> str:
    """Generate a keyword-rich search summary."""
    try:
        # Extract key themes from translation
        words = translation.lower()
        summary_parts = []
        
        if 'sorrow' in words or 'grief' in words:
            summary_parts.append("grief and sorrow")
        if 'duty' in words or 'action' in words:
            summary_parts.append("dharma and karma")
        if 'devotion' in words or 'love' in words:
            summary_parts.append("bhakti and surrender")
        if 'mind' in words or 'meditation' in words:
            summary_parts.append("mind control and meditation")
        if 'knowledge' in words or 'wisdom' in words:
            summary_parts.append("spiritual wisdom")
        
        # Default spiritual keywords
        if not summary_parts:
            summary_parts = ["spiritual wisdom", "divine truth"]
        
        summary = f"This verse teaches about {', '.join(summary_parts)}, providing guidance on the spiritual path toward Krishna consciousness, self-realization, and inner peace through yoga practice."
        
        return summary[:200]  # Limit to 200 characters
    except:
        return "Spiritual guidance from Krishna about dharma, karma, devotion, and the path to self-realization through yoga and divine wisdom."

def fallback_caption_generation(chapter, verse):
    """Enhanced fallback caption generation using CSV data when AI fails."""
    try:
        # Try CSV data first
        csv_handler = BhagavadGitaCSVHandler()
        verse_data = csv_handler.format_verse_data_for_caption(chapter, verse)
        
        if verse_data:
            log_info(f"Using enhanced CSV fallback for Chapter {chapter}, Verse {verse}")
            
            # Generate enhanced hook based on verse theme
            enhanced_hook = _generate_enhanced_hook_from_translation(verse_data['translation'])
            
            # Generate enhanced caption with CSV data
            caption = f"""భగవద్గీత {chapter}.{verse}: ఆధ్యాత్మిక జ్ఞానం

{enhanced_hook}

Chapter-Verse: {chapter}-{verse}

{verse_data['sanskrit']}

{verse_data['transliteration']}

English Translation:
{verse_data['translation']}

Word-by-Word Translation:
{verse_data['word_meanings']}

{_generate_search_summary(verse_data['translation'])}

📚 Discover more Bhagavad Gita wisdom in Telugu, English, and other Indian languages — including full chapters (like Chapter 1, 2, 12), meaningful verses (e.g., 2.47), Sanskrit + English translations, Chaganti & Ghantasala slokas, and spiritual songs.

#spiritualwisdom #krishnaconsciousness #dharma #karma #bhakti #yoga #meditation"""
            
            return caption
        
        # Final fallback - basic template
        log_warning(f"No CSV data available, using basic template for Chapter {chapter}, Verse {verse}")
        basic_caption = f"""భగవద్గీత {chapter}.{verse}: ఆధ్యాత్మిక జ్ఞానం

🕉️ భగవద్గీత నుండి దైవిక జ్ఞానం
Wisdom from the Bhagavad Gita 🌸

Chapter-Verse: {chapter}-{verse}

[Sanskrit content temporarily unavailable]

This verse from the Bhagavad Gita provides essential spiritual guidance on the path of dharma and self-realization through Krishna's teachings.

📚 Discover more Bhagavad Gita wisdom in Telugu, English, and other Indian languages — including full chapters (like Chapter 1, 2, 12), meaningful verses (e.g., 2.47), Sanskrit + English translations, Chaganti & Ghantasala slokas, and spiritual songs.

#spiritualwisdom #krishnaconsciousness #dharma #karma #bhakti"""
        
        return basic_caption
        
    except Exception as e:
        log_error(f"Error in fallback caption generation: {e}")
        return f"भगवद्गीत {chapter}.{verse} - Spiritual wisdom from the Bhagavad Gita. Chapter {chapter}, Verse {verse}."

def _generate_enhanced_hook_from_translation(translation: str) -> str:
    """Generate enhanced Telugu + English hook based on YouTube system approach."""
    try:
        # Extract key concepts for enhanced hook
        words = translation.lower().split()
        
        # Enhanced hook patterns based on verse themes (like YouTube system)
        if any(word in words for word in ['sorrow', 'grief', 'sad', 'distress']):
            return "⚔️ దుఃఖం రాజ్యాలు కూడా తీరలేవు!\nEven kingdoms cannot heal this inner sorrow ⚔️"
        elif any(word in words for word in ['duty', 'action', 'work', 'karma']):
            return "🎯 కర్మయేవాధికారస్తే మా ఫలేషు కదాచన!\nYour right is to action alone, never to its fruits 🎯"
        elif any(word in words for word in ['devotion', 'love', 'surrender', 'bhakti']):
            return "❤️ భక్తితో అర్పించిన పత్రం కూడా స్వీకరిస్తాను!\nEven a leaf offered with devotion I accept ❤️"
        elif any(word in words for word in ['mind', 'meditation', 'concentration', 'focus']):
            return "🧘 మనస్సును నియంత్రించినవాడే విజేత!\nOne who controls the mind is the true victor 🧘"
        elif any(word in words for word in ['knowledge', 'wisdom', 'understand', 'realize']):
            return "🌟 జ్ఞానమే అజ్ఞాన అంధకారాన్ని తొలగిస్తుంది!\nKnowledge alone dispels the darkness of ignorance 🌟"
        elif any(word in words for word in ['dharma', 'righteousness', 'right', 'duty']):
            return "⚖️ స్వధర్మం చేయడం పరధర్మం కంటే మేలు!\nBetter is one's own dharma than another's ⚖️"
        elif any(word in words for word in ['soul', 'atman', 'self', 'eternal']):
            return "✨ ఆత్మ ఎప్పుడూ జన్మించదు మరణించదు!\nThe soul never takes birth nor dies ✨"
        else:
            # Generic but engaging spiritual hook
            return "🕉️ ఈ శ్లోకంలో దాగి ఉంది జీవిత సత్యం!\nLife's greatest truth is hidden in this verse 🕉️"
    except:
        return "🕉️ భగవద్గీత నుండి దైవిక జ్ఞానం\nDivine wisdom from the Bhagavad Gita 🌸"
