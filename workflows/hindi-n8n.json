{"nodes": [{"parameters": {"content": "```\n╔════════════════════════════════════════════════════╗\n║                                                    ║\n║   🔵 ONLY GOOGLE-DEPENDENT $300 MODEL USAGE         ║\n║         (Google Cloud Credit Usage Limit)          ║\n║                                                    ║\n╚════════════════════════════════════════════════════╝\n```\n", "width": 640}, "type": "n8n-nodes-base.stickyNote", "position": [-180, -120], "typeVersion": 1, "id": "69af7e4b-7ff4-471b-aa92-f8fea84c6da3", "name": "Sticky Note5"}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-pro-preview-tts:generateContent?key=AIzaSyBeS7BUFrJLBPkEsDSNzDzRfWFCC1v51so", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"contents\": [\n    {\n      \"parts\": [\n        {\n          \"text\": \"{{ $json.output }}\"\n        }\n      ]\n    }\n  ],\n  \"generationConfig\": {\n    \"responseModalities\": [\"AUDIO\"],\n    \"speechConfig\": {\n      \"voiceConfig\": {\n        \"prebuiltVoiceConfig\": {\n          \"voiceName\": \"Kore\"\n        }\n      }\n    }\n  },\n  \"model\": \"gemini-2.5-pro-preview-tts\"\n}\n", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [200, 140], "id": "7d28ca07-0318-4412-bf07-198bb08ca367", "name": "HTTP Request29"}, {"parameters": {"jsCode": "return [{\n  binary: {\n    data: {\n      data: $json[\"candidates\"][0].content.parts[0].inlineData.data,\n      mimeType: 'audio/L16',\n      fileName: 'out.pcm',\n      fileExtension: 'pcm'\n    }\n  }\n}];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [420, 440], "id": "1bb8509c-121e-4bd5-b1df-c14831fce267", "name": "Code9", "alwaysOutputData": true}, {"parameters": {"method": "POST", "url": "http://ffmpeg-api-service:3003/convert", "sendQuery": true, "queryParameters": {"parameters": [{"name": "output_name", "value": "=Chapter_{{ $('Google Sheets3').item.json[\"Chapter-Verse\"].split('-')[0] }}_Verse_{{ $('Google Sheets3').item.json[\"Chapter-Verse\"].split('-')[1] }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [900, 440], "id": "b429a55d-08c8-43d6-b200-0491d2566a9e", "name": "HTTP Request30"}, {"parameters": {"operation": "write", "fileName": "/shared-data/downloads/out.pcm", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [640, 440], "id": "22d1fa97-8986-44b3-95fb-7c050d875bc5", "name": "Read/Write Files from Disk19"}, {"parameters": {"promptType": "define", "text": "=You are a Hindi voiceover scriptwriter for short educational and spiritual videos based on the Bhagavad Gita.\n\nTARGET AUDIENCE: All people seeking spiritual knowledge and wisdom.\n\nINPUT FORMAT:\n• Chapter number and verse number: {{ $json['Chapter-Verse'] }} (convert to Hindi language if it is in English numbers in the beginning sentence)\n• Simple English explanation: {{ $json['\tEnglish Translation'] }}\n\nINPUT VALIDATION & ERROR RECOVERY:\n• Expect Chapter-Verse in the format \"number-number\" (e.g., 1-3). If input is in words, convert to numeric format before using. Always convert the numbers into Hindi digits for the opening line.\n• If the English translation input is vague or missing, use your internal knowledge of Bhagavad Gita for verse {{ $json['Chapter-Verse'] }} to construct a meaningful and accurate Hindi explanation.\n• When {{ $json['Chapter-Verse'] }} is malformed or missing, default to explaining the importance of studying Bhagavad Gita in general terms while maintaining the required script structure.\n• For empty or unclear translations, draw from traditional commentaries and present the verse's core teaching in simple Hindi.\n\nSCRIPT REQUIREMENTS:\n• Language: Simple, everyday Hindi - avoid complex Sanskrit terms or scholarly language\n• Tone: Warm, humble, and reflective - like a caring spiritual elder speaking to family\n• Duration: Under 3 minutes when processed by Google Text-to-Speech\n• Character Limit: Hindi content must be below 1600 characters (minimum 1000 characters)\n\nSCRIPT STRUCTURE:\n\t1.\tBegin with this exact sentence, inserting the EXACT chapter and verse numbers:\nSay in a warm, welcoming tone: इस वीडियो में हम भगवद्गीता अध्याय [{{ $json['Chapter-Verse'].split('-')[0] }}], श्लोक [{{ $json['Chapter-Verse'].split('-')[1] }}] का अध्ययन करेंगे।\n\nIMPORTANT: Always use the exact chapter and verse numbers from the input data ({{ $json['Chapter-Verse'] }}). For example, if you receive 1-3, use अध्याय 1, श्लोक 3. Double-check that the numbers in your script match the input exactly - this is crucial for viewers to identify the correct verse being discussed. NEVER use quotation marks anywhere in your output.\n\n\t2.\tExplain the meaning and emotional essence of the verse in simple spoken Hindi:\n\t•\tIf the English translation is unclear or insufficient, use the chapter and verse numbers to search for additional Bhagavad Gita explanations for better understanding\n\t•\tUse relatable everyday examples (family relationships, daily life situations, common emotions)\n\t•\tKeep sentences short and natural for easy TTS pronunciation\n\t•\tSpeak as if addressing respected elders with love and reverence\n\t•\tUse English style prompts before key spiritual concepts (e.g., \"Speak with gentle reverence:\")\n\t•\tFor emotional parts, use prompts like \"Say in a contemplative manner:\"\n\t•\tNEVER use quotation marks - use indirect speech: उसने कहा कि वह आएगा instead of उसने कहा \"मैं आऊंगा\"\n\n\t3.\tEnd with a thoughtful spiritual reflection:\nSay in a peaceful, reflective tone: [spiritual message bringing peace and clarity]\n\n\t4.\tClose with this purpose statement:\nSpeak with gentle authority: यही इस श्लोक के भाव को समझाने का उद्देश्य है।\n\n\t5.\tAlways end the script with this exact line (mandatory):\nSay in a warm, encouraging tone: और भी भगवद्गीता श्लोकों का अर्थ जानने के लिए, हमारे अगले वीडियो जरूर देखें।\n\nCOMPREHENSIVE GOOGLE TEXT-TO-SPEECH OPTIMIZATION:\n• Use natural language prompts in English to control style, tone, and pace\n• Place English prompts immediately before the Hindi text they affect\n• Examples: \"Say in a warm, gentle tone:\", \"Speak with reverence:\", \"Say in a contemplative manner:\"\n• Use prompts strategically for key spiritual concepts, emotional moments, and important reflections\n• Maintain single-speaker consistency with varied emotional delivery\n\nADVANCED GOOGLE TTS ENHANCEMENTS:\n• SSML-compatible pauses: Add natural breathing spaces with \"pause briefly\" or \"take a short breath\" prompts\n• Pronunciation control: For any unavoidable Sanskrit terms, use phonetic Hindi equivalents\n• Prosody guidance: Use \"speak slowly and clearly\" for complex spiritual concepts\n• Volume emphasis: Use \"say with gentle emphasis\" for key teachings instead of shouting or loud delivery\n• Natural flow: Ensure smooth transitions between English prompts and Hindi content\n• Rhythm control: Vary sentence length and use \"speak in a measured pace\" for important verses\n• Emotional delivery: Layer multiple tone prompts like \"Say with warm reverence and gentle authority\"\n• Clarity enhancement: Use \"enunciate clearly\" before complex Hindi philosophical terms\n• Breathing patterns: Include \"pause to let the meaning settle\" between major concepts\n• Voice consistency: Maintain the same warm, elder-like tone throughout with varied emotional depth\n\nCHARACTER COUNT & OUTPUT FORMATTING REQUIREMENTS:\n• Hindi content: Must be between 1000-1600 characters to ensure 3-minute duration\n• Before returning the final output, count the number of Hindi characters. If it's less than 1000 or more than 1600, revise and regenerate until it fits the character window.\n• Return as ONE continuous line - NO line breaks (\\n), carriage returns (\\r), or paragraph breaks\n• Use only single spaces between sentences and prompts\n• Include English style prompts naturally within the continuous flow\n• ABSOLUTELY NO quotation marks (\") anywhere - causes parsing errors\n• Ensure that the output contains NO invisible line breaks (e.g., \\n, \\r, \\r\\n), no carriage returns, and no escaped characters. Output must be plain text.\n• Proper spacing and punctuation for smooth TTS playback\n• Convert any direct quotes to indirect speech (e.g., उसने कहा कि वह आएगा)\n\nFINAL VALIDATION CHECKLIST:\n• Scan entire text and remove any quotation marks\n• Ensure zero line breaks in the output\n• Convert any direct quotes to indirect speech\n• Count Hindi characters and verify they are between 1000-1600\n• If character count is outside range, revise and regenerate the script\n• Confirm single continuous text string format\n• Verify output is plain text with no invisible characters or line breaks\n• Ensure Hindi numbers are used in the opening sentence\n• Validate that English prompts flow naturally with Hindi content\n• Check that the script is ready for direct TTS processing", "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [-180, 440], "id": "d1db6bfd-29d4-4b2c-a229-e4bc5cc90201", "name": "AI Agent5"}, {"parameters": {"modelName": "models/gemini-2.5-pro", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [-340, 140], "id": "19216de6-99c8-4736-8d09-d108cab51024", "name": "Google Gemini Chat Model5", "credentials": {"googlePalmApi": {"id": "BPKTBRSKg2rV2oCh", "name": "shukanchowdary-300$ account"}}}, {"parameters": {"documentId": {"__rl": true, "value": "1BUgqE4bq-F6fjSSsoYm7TO36TGRfZNr5s8MQy6vO21w", "mode": "list", "cachedResultName": "Bhagavad-Gita-details", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1BUgqE4bq-F6fjSSsoYm7TO36TGRfZNr5s8MQy6vO21w/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1BUgqE4bq-F6fjSSsoYm7TO36TGRfZNr5s8MQy6vO21w/edit#gid=0"}, "filtersUI": {"values": [{"lookupColumn": "hindi status", "lookupValue": "="}]}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [-680, 440], "id": "32e4b3c4-9425-4b8b-b4e3-dd360872f1f0", "name": "Google Sheets3", "credentials": {"googleSheetsOAuth2Api": {"id": "dKe2k0nUPD4ShTRe", "name": "Google Sheets account"}}}, {"parameters": {"options": {}}, "id": "de034f31-b09e-47e9-91ad-618319cb82fb", "name": "Process One Verse at a Time2", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-460, 440]}, {"parameters": {"fileSelector": "=/shared-data{{ $json.output_file }}", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [1120, 440], "id": "f7278447-0569-4151-b23a-ddb059bfe650", "name": "Read/Write Files from Disk20"}, {"parameters": {"method": "POST", "url": "https://api.groq.com/openai/v1/audio/transcriptions", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer ********************************************************"}]}, "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"name": "model", "value": "whisper-large-v3-turbo"}, {"name": "language", "value": "hi"}, {"name": "response_format", "value": "verbose_json"}, {"name": "timestamp_granularities[]", "value": "word"}, {"parameterType": "formBinaryData", "name": "file", "inputDataFieldName": "=data"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1320, 180], "id": "69a636f5-7f7d-43e2-8999-883ce9448c8f", "name": "HTTP Request31"}, {"parameters": {"jsCode": "// ===== CONFIGURATION VARIABLES =====\nconst CONFIG = {\n  // Number of words to display on screen at a time\n  wordsToShow: 4,             // Increased for better readability on mobile\n  \n  // General subtitle styling  \n  fontName: 'Noto Sans Telugu', // Better Telugu font support\n  fontSize: 24,               // Much smaller for 432x768 resolution\n  fontColor: 'FFFFFF',        // Regular text color in BGR format (white)\n  outlineColor: '000000',     // Text outline color in BGR format (black)\n  backgroundColor: '80000000', // Semi-transparent background for readability\n  \n  // Highlight styling\n  highlightFontSize: 28,      // Slightly larger for highlight\n  highlightColor: 'FFFF00',   // Bright yellow for better visibility (BGR format)\n  \n  // Text appearance\n  bold: 1,                    // Bold for better visibility\n  italic: 0,\n  underline: 0,\n  strikeout: 0,\n  \n  // Text scaling and positioning\n  scaleX: 100,\n  scaleY: 100,\n  spacing: 2,                 // Slight letter spacing for readability\n  angle: 0,\n  \n  // Border options\n  borderStyle: 1,\n  outline: 2,                 // Thinner outline for mobile\n  shadow: 1,\n  \n  // Alignment and margins (adjusted for 432x768)\n  alignment: 2,               // Bottom center\n  marginL: 10,\n  marginR: 10,\n  marginV: 50,                // Much smaller margin for mobile format\n  \n  // Video dimensions (updated for your 432x768 format)\n  videoWidth: 432,            // Updated to match your video resolution\n  videoHeight: 768,           // Updated to match your video resolution\n  \n  // Maximum gap time between words (in seconds) before considering it a new phrase\n  maxGapTimeMs: 1500,         // Slightly reduced for smoother transitions\n  \n  // Output file name\n  outputFileName: 'telugu_karaoke_subtitles.ass',\n  \n  // Subtitle display mode: 'karaoke' or 'simple'\n  displayMode: 'simple',      // Use 'simple' for better compatibility\n  \n  // Minimum display time for each subtitle (in seconds)\n  minDisplayTime: 0.5\n};\n\n// Parse input data - handle both array and direct object format\n// Fix for HTTP response that returns array format\nlet inputData;\nif (Array.isArray(items[0].json)) {\n  // HTTP response is array format: [{ \"task\": \"transcribe\", \"words\": [...] }]\n  inputData = items[0].json[0];\n} else if (items[0].json && items[0].json.words) {\n  // Direct object format: { \"words\": [...] }\n  inputData = items[0].json;\n} else {\n  // Alternative format where words might be directly in json\n  inputData = items[0].json;\n}\n\nconst words = inputData.words || [];\n\n// Debug logging to help troubleshoot\nconsole.log('Input data structure:', {\n  isArray: Array.isArray(items[0].json),\n  hasWords: !!inputData.words,\n  wordCount: words.length,\n  firstWord: words[0] ? words[0].word : 'none',\n  dataKeys: Object.keys(inputData)\n});\n\n// Validate words data\nif (!words || words.length === 0) {\n  return [{\n    json: {\n      error: 'No words found in input data',\n      inputStructure: typeof items[0].json,\n      inputKeys: items[0].json ? Object.keys(items[0].json) : 'null'\n    }\n  }];\n}\n\n// Validate word timing data\nconst validWords = words.filter(word => \n  word.word && \n  typeof word.start === 'number' && \n  typeof word.end === 'number' && \n  word.start >= 0 && \n  word.end > word.start\n);\n\nif (validWords.length === 0) {\n  return [{\n    json: {\n      error: 'No valid word timing data found',\n      wordCount: words.length,\n      sampleWord: words[0]\n    }\n  }];\n}\n\nconsole.log(`Processing ${validWords.length} valid words out of ${words.length} total words`);\n\n// ASS Header with proper formatting\nlet ass = `[Script Info]\nTitle: Telugu Karaoke Subtitles\nScriptType: v4.00+\nPlayResX: ${CONFIG.videoWidth}\nPlayResY: ${CONFIG.videoHeight}\n\n[V4+ Styles]\nFormat: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding\nStyle: Default,${CONFIG.fontName},${CONFIG.fontSize},&H${CONFIG.fontColor},&H${CONFIG.outlineColor},&H${CONFIG.outlineColor},&H${CONFIG.backgroundColor},${CONFIG.bold},${CONFIG.italic},${CONFIG.underline},${CONFIG.strikeout},${CONFIG.scaleX},${CONFIG.scaleY},${CONFIG.spacing},${CONFIG.angle},${CONFIG.borderStyle},${CONFIG.outline},${CONFIG.shadow},${CONFIG.alignment},${CONFIG.marginL},${CONFIG.marginR},${CONFIG.marginV},1\n\n[Events]\nFormat: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text\n`;\n\n/**\n * Helper: convert seconds to ASS time format (H:MM:SS.cs)\n */\nfunction formatTimeASS(sec) {\n  const h = Math.floor(sec / 3600);\n  const m = Math.floor((sec % 3600) / 60);\n  const s = Math.floor(sec % 60);\n  const cs = Math.floor((sec - Math.floor(sec)) * 100);\n  return `${h}:${m.toString().padStart(2, '0')}:${s.toString().padStart(2, '0')}.${cs.toString().padStart(2, '0')}`;\n}\n\n/**\n * Creates the subtitle text with the specified word highlighted within a fixed group\n * Simplified for better FFmpeg compatibility\n */\nfunction createSubtitleText(wordGroup, highlightIndex) {\n  let line = '';\n  \n  wordGroup.forEach((word, i) => {\n    // Add space between words (not before the first word)\n    if (i > 0) line += ' ';\n    \n    // Apply highlight to the specified word - simplified approach\n    if (i === highlightIndex) {\n      // Use simpler karaoke tags that work better with FFmpeg\n      line += `{\\\\k100}${word.word.trim()}`;\n    } else {\n      line += word.word.trim();\n    }\n  });\n  \n  return line;\n}\n\n/**\n * Alternative: Create simple subtitles without karaoke effects\n * Use this if karaoke doesn't work well\n */\nfunction createSimpleSubtitleText(wordGroup) {\n  return wordGroup.map(word => word.word.trim()).join(' ');\n}\n\n/**\n * Create groups of words that respect natural pauses and sentence boundaries\n * Improved for Telugu text\n */\nfunction createWordGroups(words) {\n  const groups = [];\n  let currentGroup = [];\n  \n  for (let i = 0; i < words.length; i++) {\n    const currentWord = words[i];\n    currentGroup.push(currentWord);\n    \n    // Check if this word ends with punctuation (sentence boundary)\n    const endsWithPunctuation = /[।.!?॥]$/.test(currentWord.word.trim());\n    \n    // Calculate gap to next word\n    const nextWord = words[i + 1];\n    const gap = nextWord ? (nextWord.start - currentWord.end) : 0;\n    \n    // Start a new group if:\n    // 1. We've reached the maximum words per group, OR\n    // 2. We've encountered punctuation (end of sentence), OR\n    // 3. There's a significant pause (> 1 second), OR\n    // 4. We're at the last word\n    if (currentGroup.length >= CONFIG.wordsToShow || \n        endsWithPunctuation || \n        gap > 1.0 || \n        i === words.length - 1) {\n      if (currentGroup.length > 0) {\n        groups.push([...currentGroup]);\n        currentGroup = [];\n      }\n    }\n  }\n  \n  // Add any remaining words\n  if (currentGroup.length > 0) {\n    groups.push(currentGroup);\n  }\n  \n  return groups;\n}\n\n// Create word groups that respect natural speech patterns\nconst wordGroups = createWordGroups(validWords);\nconst dialogueLines = [];\n\nif (CONFIG.displayMode === 'simple') {\n  // Simple mode: show each word group as a complete subtitle\n  for (let groupIndex = 0; groupIndex < wordGroups.length; groupIndex++) {\n    const wordGroup = wordGroups[groupIndex];\n    \n    if (wordGroup.length === 0) continue;\n    \n    const startTime = wordGroup[0].start;\n    const endTime = Math.max(wordGroup[wordGroup.length - 1].end, startTime + CONFIG.minDisplayTime);\n    const formattedStart = formatTimeASS(startTime);\n    const formattedEnd = formatTimeASS(endTime);\n    const subtitleText = createSimpleSubtitleText(wordGroup);\n    \n    dialogueLines.push({\n      start: startTime,\n      end: endTime,\n      formattedStart,\n      formattedEnd,\n      text: subtitleText,\n      group: groupIndex\n    });\n  }\n} else {\n  // Karaoke mode: highlight individual words (original logic)\n  for (let i = 0; i < validWords.length; i++) {\n    const currentWord = validWords[i];\n    \n    // Find which group contains this word\n    let groupIndex = -1;\n    let positionIndex = -1;\n    \n    for (let g = 0; g < wordGroups.length; g++) {\n      const wordIndex = wordGroups[g].findIndex(w => \n        w.start === currentWord.start && w.end === currentWord.end && w.word === currentWord.word);\n      \n      if (wordIndex !== -1) {\n        groupIndex = g;\n        positionIndex = wordIndex;\n        break;\n      }\n    }\n    \n    if (groupIndex === -1) continue;\n    \n    const wordGroup = wordGroups[groupIndex];\n    \n    // Create subtitle with the correct word highlighted\n    const startTime = currentWord.start;\n    const endTime = Math.max(currentWord.end, startTime + CONFIG.minDisplayTime);\n    const formattedStart = formatTimeASS(startTime);\n    const formattedEnd = formatTimeASS(endTime);\n    const subtitleText = createSubtitleText(wordGroup, positionIndex);\n    \n    // Add this subtitle to our list\n    dialogueLines.push({\n      start: startTime,\n      end: endTime,\n      formattedStart,\n      formattedEnd,\n      text: subtitleText,\n      group: groupIndex,\n      position: positionIndex\n    });\n  }\n}\n\n// Fill gaps between words in the same group to maintain visual continuity\nfor (let i = 0; i < dialogueLines.length - 1; i++) {\n  const current = dialogueLines[i];\n  const next = dialogueLines[i + 1];\n  \n  // If they're in the same group and there's a small gap\n  if (current.group === next.group && \n      next.start - current.end > 0 && \n      next.start - current.end <= CONFIG.maxGapTimeMs / 1000) {\n    // Fill the gap by extending the current line's end time\n    current.end = next.start;\n    current.formattedEnd = formatTimeASS(current.end);\n  }\n}\n\n// Write all dialogue lines to the ASS file\ndialogueLines.forEach(line => {\n  ass += `Dialogue: 0,${line.formattedStart},${line.formattedEnd},Default,,0,0,0,,${line.text}\\n`;\n});\n\n// Add debug information as comments\nass += `\\n; Generated ASS file info:\\n`;\nass += `; Mode: ${CONFIG.displayMode}\\n`;\nass += `; Resolution: ${CONFIG.videoWidth}x${CONFIG.videoHeight}\\n`;\nass += `; Total dialogue lines: ${dialogueLines.length}\\n`;\nass += `; Word groups: ${wordGroups.length}\\n`;\nass += `; Font: ${CONFIG.fontName}, Size: ${CONFIG.fontSize}\\n`;\n\n// Note: File system operations are not available in n8n Code node\n// The ASS file will be returned as binary data for n8n to handle\nlet fileSaveStatus = 'handled_by_n8n';\nlet fileSavePath = 'returned_as_binary_data';\n\nconsole.log(`✅ ASS file generated successfully (${ass.length} characters)`);\nconsole.log(`📄 File will be available as binary data in n8n workflow`);\n\n// Create base64 encoded string for n8n\nconst assBase64 = Buffer.from(ass, 'utf8').toString('base64');\n\n// Return as binary ASS file with improved metadata\nreturn [{\n  binary: {\n    data: {\n      data: assBase64,\n      mimeType: 'text/ass',\n      fileName: CONFIG.outputFileName\n    }\n  },\n  json: {\n    success: true,\n    mode: CONFIG.displayMode,\n    resolution: `${CONFIG.videoWidth}x${CONFIG.videoHeight}`,\n    dialogueLines: dialogueLines.length,\n    wordGroups: wordGroups.length,\n    totalWords: validWords.length,\n    originalWords: words.length,\n    fileSaved: fileSaveStatus,\n    filePath: fileSavePath,\n    fileSize: ass.length,\n    info: `Generated ASS file with ${dialogueLines.length} subtitle lines for ${CONFIG.videoWidth}x${CONFIG.videoHeight} video`,\n    note: 'ASS file returned as binary data - use n8n Write Binary File node to save to disk'\n  }\n}];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1540, 460], "id": "3f73ab83-9b21-46e1-8f82-362120e811e8", "name": "Code10"}, {"parameters": {"operation": "write", "fileName": "=/shared-data/ass-scripts/{{ $node[\"Read/Write Files from Disk20\"].json[\"fileName\"].replace('.mp3', '.ass') }}", "dataPropertyName": "=data", "options": {"append": false}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [1760, 460], "id": "cb340385-5e94-45ee-a343-a948c6d6a18d", "name": "Save ass2"}, {"parameters": {"method": "POST", "url": "http://video-service:3002/create-enhanced-video", "sendBody": true, "bodyParameters": {"parameters": [{"name": "audioFileName", "value": "={{ $('Read/Write Files from Disk20').item.json.fileName }}"}, {"name": "assFileName", "value": "={{ $('Save ass2').item.json.fileName.split('/').pop()}}"}, {"name": "backgroundImage", "value": "={{ $json.fileName.split('/').pop() }}"}]}, "options": {"response": {"response": {"neverError": true, "responseFormat": "json"}}, "timeout": 900000}}, "id": "e7976bd3-5311-4248-ba58-7c7c9d6895fd", "name": "Create Video1", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1720, 1420], "executeOnce": true}, {"parameters": {"resource": "video", "operation": "upload", "title": "={{ $('AI Agent6').item.json.output.title }}", "regionCode": "IN", "categoryId": "27", "options": {"defaultLanguage": "hi", "description": "={{ $('AI Agent6').item.json.output.description }}", "license": "youtube", "privacyStatus": "private", "selfDeclaredMadeForKids": false, "tags": "={{ $('AI Agent6').item.json.output.tags }}{{ $('AI Agent6').item.json.output.trendingHashtags }}"}}, "type": "n8n-nodes-base.youTube", "typeVersion": 1, "position": [2940, 1420], "id": "6810f1c4-8bfc-4363-b7bd-049b33e4e7ea", "name": "YouTube", "credentials": {"youTubeOAuth2Api": {"id": "f88z7xW4AWxRk6df", "name": "bhagavad-gita-account-api"}}}, {"parameters": {"promptType": "define", "text": "=Analyze the following video script to generate YouTube Shorts metadata:\n\n{{ $('AI Agent5').item.json.output }}", "hasOutputParser": true, "options": {"systemMessage": "=You are a helpful AI assistant that generates YouTube Shorts metadata.\n\nYour task is to analyze the provided video script and return a JSON object with the following **strictly structured fields**, suitable for use in a YouTube upload API:\n\nRequired Fields:\n- `title`: A short, engaging YouTube Shorts title. Must include `#shorts`.\n- `description`: A well-structured YouTube Shorts description following these guidelines:\n  - **Hook & Keywords (first 100-150 characters)**: Start with primary keywords and compelling hook visible before \"Show More\"\n  - **Concise Summary (1-3 sentences)**: Clearly describe video content in natural language, vary phrasing from title for broader keyword reach\n  - **Call-to-Action**: Include engagement prompt like \"👉 Subscribe for weekly spiritual wisdom!\"\n  - **Channel Link**: Include \"🔗 More videos: https://www.youtube.com/@BhagavadGita-A-I\"\n  - **Hashtags**: Include 3-5 relevant hashtags at the end of the description (YouTube displays first 3 above title)\n  - **Formatting**: Use line breaks and mobile-friendly formatting\n  - **Must be in English language regardless of the script language**\n  - Always end with: \"This video is AI-generated content. For any concerns or corrections, please contact the admin.\"\n- `tags`: A list of 3 to 7 relevant keywords (strings), helpful for discovery. Include trending hashtags based on the script content and topic. Each tag must start with # symbol.\n- `trendingHashtags`: A list of 3 to 5 trending hashtags (strings) relevant to the script content. Each hashtag must start with # symbol.\n- `defaultLanguage`: The ISO 639-1 language code of the video (e.g., \"en\" for English, \"hi\" for Hindi).\n\nOutput Requirements:\n- Return **only** a valid JSON object with the fields above.\n- Do **not** include any explanation, markdown, or extra text.\n- Do **not** include headers like \"Here is your required...\" or footers like \"Do you need any other details?\".\n- Do **not** add any conversational elements, greetings, or follow-up questions.\n- Be strict: ensure the JSON can be parsed programmatically.\n- The response should start directly with `{` and end with `}`.\n\n## ✅ Expected Output Format\n```json\n{\n  \"title\": \"भगवद्गीता श्लोक 1.3 व्याख्या - #shorts\",\n  \"description\": \"🕉️ Discover the profound wisdom of Bhagavad Gita verse 1.3 in Hindi - ancient spiritual guidance for modern life!\\n\\nThis video breaks down the sacred verse with clear explanations and practical insights you can apply today. Perfect for spiritual seekers and those exploring Hindu philosophy.\\n\\n👉 Subscribe for weekly spiritual wisdom!\\n\\n🔗 More videos: https://www.youtube.com/@BhagavadGita-A-I\\n\\n#BhagavadGita #Hindi #Spirituality #shorts #wisdom\\n\\nThis video is AI-generated content. For any concerns or corrections, please contact the admin.\",\n  \"tags\": [\"#BhagavadGita\", \"#Hindi\", \"#Spiritual\", \"#shorts\"],\n  \"trendingHashtags\": [\"#spirituality\", \"#wisdom\", \"#lifelessons\", \"#mindfulness\", \"#innerpeace\"],\n  \"defaultLanguage\": \"hi\"\n}\n```\n\nAssume the video is a spiritual or educational Shorts video unless the script suggests otherwise."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [2100, 1420], "id": "322598be-3499-4337-a174-a812d3bee61f", "name": "AI Agent6", "executeOnce": true}, {"parameters": {"modelName": "models/gemini-2.5-pro", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [2040, 1700], "id": "2de4ebd6-fb66-417f-989e-4d2e8c2032a4", "name": "Google Gemini Chat Model6", "credentials": {"googlePalmApi": {"id": "BPKTBRSKg2rV2oCh", "name": "shukanchowdary-300$ account"}}}, {"parameters": {"jsonSchemaExample": "{\n  \"title\": \"భగవద్గీత శ్లోకము 1.3 వివరణ - #shorts\",\n  \"description\": \"This video explains the meaning of Bhagavad Gita verse 1.3 in Telugu. #shorts This video is AI-generated content. For any concerns or corrections, please contact the admin.\",\n  \"tags\": [\"Bhagavad Gita\", \"Telugu\", \"Spiritual\", \"shorts\"],\n  \"trendingHashtags\": [\"spirituality\", \"wisdom\", \"lifelessons\", \"mindfulness\", \"innerpeace\"],\n  \"defaultLanguage\": \"te\"\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [2360, 1660], "id": "05a023cb-a92b-474f-ab3e-d02d7f45b19c", "name": "Structured Output Parser"}, {"parameters": {"fileSelector": "={{ $('Read/Write Files from Disk21').item.json.fileName.replace('images', 'videos').replace('.jpg', '.mp4') }}", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [2640, 1420], "id": "a5adbfce-2b69-4dd7-8f7a-2192650dab62", "name": "Read/Write Files from Disk29", "executeOnce": true}, {"parameters": {"method": "POST", "url": "=https://www.googleapis.com/upload/youtube/v3/thumbnails/set?videoId={{ $('YouTube').item.json.uploadId }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "youTubeOAuth2Api", "sendBody": true, "contentType": "binaryData", "inputDataFieldName": "data", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2680, 2780], "id": "352b289c-f18b-487e-95d3-bac7bd2b95aa", "name": "HTTP Request41", "credentials": {"youTubeOAuth2Api": {"id": "f88z7xW4AWxRk6df", "name": "bhagavad-gita-account-api"}}}, {"parameters": {"fileSelector": "/shared-data/media/image.png", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [2340, 2780], "id": "58f12a2d-7d17-4092-b606-5fde15156cf3", "name": "Read/Write Files from Disk30"}, {"parameters": {"content": "```\n╔════════════════════════════════════════════════════╗\n║                                                    ║\n║  📸 THESE TWO NODES ARE USED TO SET THUMBNAIL FOR  ║\n║           UPLOADED YOUTUBE VIDEO                   ║\n║                                                    ║\n╚════════════════════════════════════════════════════╝\n```\n", "width": 600}, "type": "n8n-nodes-base.stickyNote", "position": [2280, 2640], "typeVersion": 1, "id": "79b0bf78-d3ca-4603-a2da-2d84aa1959b9", "name": "Sticky Note7"}, {"parameters": {"operation": "update", "documentId": {"__rl": true, "value": "1BUgqE4bq-F6fjSSsoYm7TO36TGRfZNr5s8MQy6vO21w", "mode": "list", "cachedResultName": "Bhagavad-Gita-details", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1BUgqE4bq-F6fjSSsoYm7TO36TGRfZNr5s8MQy6vO21w/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1BUgqE4bq-F6fjSSsoYm7TO36TGRfZNr5s8MQy6vO21w/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"Chapter-Verse": "={{ $('Read/Write Files from Disk29').item.json.fileName.match(/Chapter_(\\d+)_Verse_(\\d+)\\.mp4/).slice(1).join('-') }}", "hindi status": "hindi video is created and upload to youtube check it"}, "matchingColumns": ["Chapter-Verse"], "schema": [{"id": "S.No", "displayName": "S.No", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": true}, {"id": "Chapter-Verse", "displayName": "Chapter-Verse", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Audio Link", "displayName": "Audio Link", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": true}, {"id": "Sanskrit Shloka", "displayName": "Sanskrit Shloka", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": true}, {"id": "Romanized Transliteration", "displayName": "Romanized Transliteration", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": true}, {"id": "\tEnglish Translation", "displayName": "\tEnglish Translation", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": true}, {"id": "Word-by-Word Translation", "displayName": "Word-by-Word Translation", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": true}, {"id": "image-link", "displayName": "image-link", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": true}, {"id": "thumnail-link", "displayName": "thumnail-link", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": true}, {"id": "video-link", "displayName": "video-link", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": true}, {"id": "status", "displayName": "status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": true}, {"id": "hindi status", "displayName": "hindi status", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "row_number", "displayName": "row_number", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "readOnly": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [3260, 1420], "id": "3742a24b-3430-4c70-8d06-17ad86cca187", "name": "Google Sheets4", "credentials": {"googleSheetsOAuth2Api": {"id": "dKe2k0nUPD4ShTRe", "name": "Google Sheets account"}}}, {"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-980, 420], "id": "d1ec639b-bd18-415b-8ba7-e2b5bad4992c", "name": "When clicking ‘Execute workflow’"}, {"parameters": {"operation": "write", "fileName": "=/shared-data/images/{{ $('Save ass2').item.json.fileName.replace('/shared-data/ass-scripts/', '').replace('.ass', '.jpg') }}", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [840, 760], "id": "********-b9c3-40bb-8bec-60b94faa51b4", "name": "Read/Write Files from Disk21"}, {"parameters": {"operation": "write", "fileName": "=/shared-data/images/{{ $('Save ass2').item.json.fileName.replace('/shared-data/ass-scripts/', '').replace('.ass', 'b.jpg') }}", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [860, 1180], "id": "608aeab3-03bb-4d42-acd2-d938e9ab9829", "name": "Read/Write Files from Disk22"}, {"parameters": {"operation": "write", "fileName": "=/shared-data/images/{{ $('Save ass2').item.json.fileName.replace('/shared-data/ass-scripts/', '').replace('.ass', 'c.jpg') }}", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [860, 1380], "id": "4329cc5d-e26c-4367-9909-095e5c765d5c", "name": "Read/Write Files from Disk23"}, {"parameters": {"operation": "write", "fileName": "=/shared-data/images/{{ $('Save ass2').item.json.fileName.replace('/shared-data/ass-scripts/', '').replace('.ass', 'f.jpg') }}", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [860, 1960], "id": "eeda9335-519b-4a4c-9682-024e754b0b2e", "name": "Read/Write Files from Disk24"}, {"parameters": {"operation": "write", "fileName": "=/shared-data/images/{{ $('Save ass2').item.json.fileName.replace('/shared-data/ass-scripts/', '').replace('.ass', 'g.jpg') }}", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [860, 2160], "id": "b9017795-fc9f-464a-9f09-0e74e35d68a2", "name": "Read/Write Files from Disk25"}, {"parameters": {"operation": "write", "fileName": "=/shared-data/images/{{ $('Save ass2').item.json.fileName.replace('/shared-data/ass-scripts/', '').replace('.ass', 'd.jpg') }}", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [860, 1560], "id": "26fbabc5-bdc0-4833-bd32-2d2b94d0e546", "name": "Read/Write Files from Disk26"}, {"parameters": {"operation": "write", "fileName": "=/shared-data/images/{{ $('Save ass2').item.json.fileName.replace('/shared-data/ass-scripts/', '').replace('.ass', 'e.jpg') }}", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [860, 1760], "id": "af12a486-528a-4bae-bcec-11cab5657578", "name": "Read/Write Files from Disk27"}, {"parameters": {"operation": "write", "fileName": "=/shared-data/images/{{ $('Save ass2').item.json.fileName.replace('/shared-data/ass-scripts/', '').replace('.ass', 'a.jpg') }}", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [840, 980], "id": "b6454ec9-1c42-4c16-b7d4-b67295d6ba18", "name": "Read/Write Files from Disk28"}, {"parameters": {"numberInputs": 8}, "type": "n8n-nodes-base.merge", "typeVersion": 3.2, "position": [1460, 1320], "id": "96fb7f61-3af5-4015-aacc-7548b71943c9", "name": "Merge2"}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/imagen-4.0-ultra-generate-preview-06-06:predict?key=AIzaSyB5KR9vlQ-AE0Jdjx3luHgCpSLiu8xcXSs", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ \nJSON.stringify({\n  instances: [\n    {\n      prompt: \n        $json.output + \n        \" Embed a banner directly into the top part of the image, seamlessly integrated into the artwork. The banner must contain ONLY the text: Chapter \" +\n        $('Read/Write Files from Disk20').item.json.fileName.match(/Chapter_(\\d+)_Verse_\\d+/)?.[1] +\n        \", Verse \" +\n        $('Read/Write Files from Disk20').item.json.fileName.match(/Chapter_\\d+_Verse_(\\d+)/)?.[1] +\n        \". IMPORTANT: Ignore any other text content that may be present in the image prompt above. Do not include any additional text, labels, titles (such as 'Bhagavad Gita'), quotes, verses, descriptions, or any other textual content from the prompt - ONLY display the chapter and verse numbers as specified. Do not include any symbols, decorative script, or other text elements. Use Sanskrit-inspired calligraphy (in English letters) with golden or saffron text on a rich red or dark scroll-like background. Make sure the banner blends naturally into the image as a part of the visual composition, not a floating element or external frame.\"\n    }\n  ],\n  parameters: {\n    numberOfImages: 1,\n    aspectRatio: \"9:16\",\n    personGeneration: \"allow_all\"\n  }\n})\n}}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [40, 760], "id": "09f3dd14-415e-458f-8bd2-a520c311b07f", "name": "HTTP Request42"}, {"parameters": {"operation": "toBinary", "sourceProperty": "predictions[0].bytesBase64Encoded", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [420, 760], "id": "d79a687d-f644-4a47-a3bb-1f2d11b73a3f", "name": "Convert to File4"}, {"parameters": {"promptType": "define", "text": "=Hindi Bhagavad Gita Verse script:\n{{ $('AI Agent5').item.json.output }}", "options": {"systemMessage": "=You are a masterful image generation engine trained in various global art styles, with a deep understanding of historical, mythological, and spiritual narratives. Your task is to analyze the following Hindi verse from the Bhagavad Gita and write a rich, vivid, and emotionally expressive English image generation prompt optimized for advanced AI text-to-image models.\n\n### Purpose\nThe output prompt should be designed for advanced AI text-to-image models and must:\n- Translate the essence of the Bhagavad Gita verse into a visually stunning, culturally authentic, and realistic image.\n- Ensure the image captures the mood, characters, symbolic meaning, spiritual depth, and relevant weather conditions of the verse with photorealistic quality.\n- Generate realistic, lifelike imagery that maintains cultural authenticity while appearing as natural and believable as possible.\n- If the provided Hindi verse lacks specific characters, generate an image based on general Bhagavad Gita themes, such as war scenes, weather conditions, or symbolic elements, ensuring the image remains meaningful, realistic, and aligned with the essence of the verse.\n\n### Advanced AI Model Optimization Guidelines\n#### Prompt Structure\n- Keep the total prompt under 480 tokens (optimal length for most advanced models).\n- Start with clear subject identification, followed by context/background, then style specifications.\n- Use descriptive and clear language with meaningful keywords and modifiers.\n- Structure prompts as: Subject + Context + Style for optimal results.\n\n#### Photography Enhancement (when applicable)\n- Begin photography-focused prompts with \"A photo of...\" or \"A photograph of...\" to ensure realistic output.\n- Include specific camera settings when relevant: \"4K\", \"HDR\", \"studio photo\"\n- Specify lens types for enhanced realism: \"35mm\", \"50mm\", \"macro lens\", \"wide angle\"\n- Add photography modifiers: \"close-up\", \"aerial view\", \"natural lighting\", \"dramatic lighting\"\n- Include film types when appropriate: \"black and white\", \"film noir\", \"vintage film\"\n- Emphasize photorealistic quality with terms like: \"photorealistic\", \"lifelike\", \"realistic\", \"natural\"\n\n#### Quality Enhancement Keywords\n- General modifiers: \"high-quality\", \"beautiful\", \"detailed\", \"professional\", \"realistic\", \"photorealistic\", \"cinematic quality\"\n- For photorealistic results: \"4K\", \"HDR\", \"DSLR-shot\", \"taken by a professional cinematographer\", \"lifelike\", \"natural\", \"realistic\"\n- For cinematic styles: \"professional film look\", \"detailed\", \"masterpiece\", \"realistic rendering\", \"live-action style\"\n- Realism enhancers: \"photorealistic\", \"lifelike\", \"natural appearance\", \"realistic textures\", \"authentic details\"\n\n#### Cinematic Realism Enhancement\n- Use cinematic terminology: \"cinematic scene\", \"medium shot\", \"close-up\", \"wide shot\", \"aerial view\", \"live-action style composition\"\n- Specify camera techniques: \"shot on 50mm lens\", \"shallow depth of field\", \"bokeh effect\", \"lens flare\"\n- Include film-quality lighting: \"volumetric lighting\", \"backlit\", \"golden hour cinematography\", \"natural sunlight\"\n- Reference film genres for mood: \"like an epic historical drama\", \"styled like a mythological period film\", \"with the emotional tone of an epic war film\"\n\n### General Requirements\n- Generate images in vertical portrait orientation format (tall and narrow composition) - optimized for mobile viewing and portrait displays.\n- Ensure high resolution, detailed rendering, culturally accurate aesthetics, emotionally moving composition, and photorealistic quality.\n- Include quality modifiers: \"high-quality\", \"detailed\", \"beautiful\", \"professional\", \"realistic\", \"photorealistic\"\n- Generate realistic, lifelike imagery that appears natural and believable while maintaining spiritual and cultural authenticity.\n- Do not include any direct Hindi or English text in the image (focus on visual elements for this use case).\n- Be inspired by the visual richness and wisdom of the Bhagavad Gita while ensuring realistic representation.\n- Provide explicit details about characters, facial expressions, body positions, interactions, cultural context, and weather conditions optimized for AI understanding with emphasis on realistic rendering.\n- Use descriptive adjectives and adverbs to paint a clear, realistic picture for the AI model.\n- Reference specific artistic styles or movements to guide the AI's aesthetic choices while maintaining photorealistic quality.\n- Ensure all characters and elements are placed within a vertical portrait frame without missing any important details, arranging them realistically in the best positions and places to make the image look beautiful, natural, and memorable, reflecting the essence of the verse.\n\n### Specific Details\n#### Characters\n- Depict all characters mentioned in the verse with historically appropriate Mahabharata-era attire, including dhotis, turbans, jewelry, armor, divine symbols, and accessories like bows or chariots.\n- Include detailed descriptions of characters and their genders to provide context for AI character generation.\n- For portraits or character-focused scenes, use \"portrait\" keyword and specify facial details as focus.\n- Ensure all characters are represented with specific body positions, facial expressions, and precise positions within the composition.\n- Define the hierarchy of characters, highlighting central figures and their importance relative to supporting ones.\n- Include character interactions if found in the verse, using clear descriptive language.\n\n#### Scene Composition and Context\n- Structure prompts with clear subject-context-style hierarchy for optimal AI processing.\n- Provide background context: specify whether indoor, outdoor, natural environment, or cinematic setting.\n- Arrange characters and elements in balanced, visually appealing compositions that support narrative flow.\n- Ensure the image tells a narrative flow aligned with the verse, guiding viewers through emotional and philosophical depth.\n- Incorporate relevant weather conditions using cinematic descriptors: \"golden hour lighting\", \"dramatic storm clouds\", \"soft morning mist\", \"dust particles in air\".\n\n#### Cultural Context and Cinematic Realism\n- Focus on historically accurate Mahabharata-era details: \"realistic ancient Indian armor\", \"authentic dhoti and turban\", \"natural human anatomy\"\n- Include realistic cultural elements: \"ancient Indian battlefield\", \"natural stone architecture\", \"realistic weapon designs\"\n- Use natural color descriptions: \"warm earth tones\", \"natural skin tones\", \"realistic fabric textures\", \"weathered metal surfaces\"\n- Add material-specific realism: \"realistic cotton texture\", \"embossed silk detail\", \"weathered leather\", \"handwoven fabric patterns\"\n- Emphasize human emotion and drama: \"intense facial expressions\", \"natural body language\", \"realistic human interactions\"\n\n#### Lighting and Atmosphere Enhancement\n- Use cinematic lighting descriptors: \"volumetric lighting\", \"backlit\", \"lens flare\", \"natural sunlight\", \"golden hour cinematography\"\n- Apply emotionally resonant lighting: \"dramatic shadows\", \"soft natural light\", \"warm sunbeams\", \"atmospheric haze\"\n- Specify cinematic context: \"shot with shallow focus\", \"depth of field\", \"bokeh effect\", \"film grain texture\"\n- Include atmospheric details: \"dust particles in air\", \"natural fog\", \"swirling wind effects\", \"realistic weather conditions\"\n\n#### Symbolism and Realistic Materials\n- Incorporate meaningful spiritual symbols with photorealistic detail: \"realistic lotus flowers\", \"weathered golden chariots\", \"natural sunbeams\", \"carved conch shells\"\n- Add subtle divine presence elements: \"natural light rays\", \"atmospheric glow\", \"realistic celestial effects\"\n- Use realistic material descriptors: \"weathered gold\", \"carved stone\", \"natural silk fabric\", \"battle-worn metal armor\"\n- If the verse lacks specific characters, focus on realistic environmental elements and natural Bhagavad Gita themes to create meaningful visuals.\n\n### Dynamic Elements and Motion\n- Add cinematic motion using dynamic descriptors: \"flowing silk robes\", \"wind-blown hair\", \"swirling dust particles\", \"natural light movement\"\n- Include realistic weather dynamics: \"storm clouds gathering\", \"gentle rain\", \"golden sunbeams breaking through clouds\", \"atmospheric mist\"\n- For action scenes, use: \"dynamic movement\", \"natural motion blur\", \"realistic action\", \"cinematic composition\"\n- Add cinematic elements such as flowing robes, wind-blown flags, dust in the air, natural sunlight, or atmospheric effects to convey urgency, emotion, or transformation.\n- Include realistic weather-related dynamics, such as swirling winds, rain, or glowing sunlight, to enhance the scene's depth and emotional impact.\n\n### Step-by-Step Approach\n- Structure the prompt following the Subject + Context + Style framework.\n- Maintain focus on the 480-token limit while providing maximum detail and clarity.\n- Include optimized quality modifiers and descriptive language throughout.\n- Ensure every element contributes to generating a high-quality image optimized for advanced AI capabilities.\n- Think step-by-step and utilize AI strengths in detailed rendering, cultural authenticity, and artistic style interpretation.\n\n🎯 Your Output:\nProvide only the image generation prompt in a single line without any line breaks, and without any explanation or introductory text.\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [-1220, 1280], "id": "9c1eefb6-2e43-41dd-9155-b20aa96e48f8", "name": "AI Agent7"}, {"parameters": {"modelName": "models/gemini-2.5-pro", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [-1000, 1620], "id": "937c2b70-89a5-4991-980b-ccf560a811ef", "name": "Google Gemini Chat Model7", "credentials": {"googlePalmApi": {"id": "BPKTBRSKg2rV2oCh", "name": "shukanchowdary-300$ account"}}}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/imagen-3.0-generate-002:predict?key=AIzaSyB5KR9vlQ-AE0Jdjx3luHgCpSLiu8xcXSs", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"instances\": [\n    {\n      \"prompt\": \"{{ $json.output }}\"\n    }\n  ],\n  \"parameters\": {\n    \"numberOfImages\": 1,\n    \"aspectRatio\": \"9:16\",\n    \"personGeneration\": \"allow_all\"\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [60, 1180], "id": "e0f288f6-e0b1-4ad5-84f2-9c170d5419d5", "name": "HTTP Request45"}, {"parameters": {"operation": "toBinary", "sourceProperty": "predictions[0].bytesBase64Encoded", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [440, 1180], "id": "e79fb0b7-a2f7-42e1-8c99-65c7db171534", "name": "Convert to File5"}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/imagen-3.0-generate-002:predict?key=AIzaSyB5KR9vlQ-AE0Jdjx3luHgCpSLiu8xcXSs", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"instances\": [\n    {\n      \"prompt\": \"{{ $json.output }}\"\n    }\n  ],\n  \"parameters\": {\n    \"numberOfImages\": 1,\n    \"aspectRatio\": \"9:16\",\n    \"personGeneration\": \"allow_all\"\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [40, 1980], "id": "34dea17d-c5cf-4c05-8be3-42458cd2fb17", "name": "HTTP Request50"}, {"parameters": {"operation": "toBinary", "sourceProperty": "predictions[0].bytesBase64Encoded", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [440, 1980], "id": "29684a6d-6a00-4190-b101-447b23ef8ffd", "name": "Convert to File6"}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/imagen-3.0-generate-002:predict?key=AIzaSyB5KR9vlQ-AE0Jdjx3luHgCpSLiu8xcXSs", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"instances\": [\n    {\n      \"prompt\": \"{{ $json.output }}\"\n    }\n  ],\n  \"parameters\": {\n    \"numberOfImages\": 1,\n    \"aspectRatio\": \"9:16\",\n    \"personGeneration\": \"allow_all\"\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [40, 2200], "id": "67e41c90-7699-41b3-8e41-8187b7220ff7", "name": "HTTP Request51"}, {"parameters": {"operation": "toBinary", "sourceProperty": "predictions[0].bytesBase64Encoded", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [460, 2200], "id": "44833193-11af-4da8-a18f-426125b7ac9c", "name": "Convert to File7"}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/imagen-3.0-generate-002:predict?key=AIzaSyB5KR9vlQ-AE0Jdjx3luHgCpSLiu8xcXSs", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"instances\": [\n    {\n      \"prompt\": \"{{ $json.output }}\"\n    }\n  ],\n  \"parameters\": {\n    \"numberOfImages\": 1,\n    \"aspectRatio\": \"9:16\",\n    \"personGeneration\": \"allow_all\"\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [40, 980], "id": "9b37b298-c12f-4f7e-9897-99469dd557d0", "name": "HTTP Request58"}, {"parameters": {"operation": "toBinary", "sourceProperty": "predictions[0].bytesBase64Encoded", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [440, 980], "id": "b9e52c52-deb0-484f-80b7-bca8969fad0e", "name": "Convert to File"}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/imagen-3.0-generate-002:predict?key=AIzaSyB5KR9vlQ-AE0Jdjx3luHgCpSLiu8xcXSs", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"instances\": [\n    {\n      \"prompt\": \"{{ $json.output }}\"\n    }\n  ],\n  \"parameters\": {\n    \"numberOfImages\": 1,\n    \"aspectRatio\": \"9:16\",\n    \"personGeneration\": \"allow_all\"\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [40, 1560], "id": "c8d73710-eca8-4897-8b76-cde4f13c80e1", "name": "HTTP Request59"}, {"parameters": {"operation": "toBinary", "sourceProperty": "predictions[0].bytesBase64Encoded", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [420, 1560], "id": "86f8585b-a933-4b5c-9f45-988f423cf0d6", "name": "Convert to File1"}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/imagen-3.0-generate-002:predict?key=AIzaSyB5KR9vlQ-AE0Jdjx3luHgCpSLiu8xcXSs", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"instances\": [\n    {\n      \"prompt\": \"{{ $json.output }}\"\n    }\n  ],\n  \"parameters\": {\n    \"numberOfImages\": 1,\n    \"aspectRatio\": \"9:16\",\n    \"personGeneration\": \"allow_all\"\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [40, 1760], "id": "ac49d771-c85d-4461-8118-4332ff4a2972", "name": "HTTP Request60"}, {"parameters": {"operation": "toBinary", "sourceProperty": "predictions[0].bytesBase64Encoded", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [420, 1760], "id": "23cc6a87-c968-4862-b71d-4676f036ab71", "name": "Convert to File2"}, {"parameters": {"method": "POST", "url": "https://generativelanguage.googleapis.com/v1beta/models/imagen-3.0-generate-002:predict?key=AIzaSyB5KR9vlQ-AE0Jdjx3luHgCpSLiu8xcXSs", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"instances\": [\n    {\n      \"prompt\": \"{{ $json.output }}\"\n    }\n  ],\n  \"parameters\": {\n    \"numberOfImages\": 1,\n    \"aspectRatio\": \"9:16\",\n    \"personGeneration\": \"allow_all\"\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [40, 1380], "id": "5b5d4b51-6144-4a9d-bc5d-66b381b5cdae", "name": "HTTP Request61"}, {"parameters": {"operation": "toBinary", "sourceProperty": "predictions[0].bytesBase64Encoded", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [420, 1380], "id": "57f00ada-2e48-43f6-90e9-9a28e6809e6f", "name": "Convert to File3"}], "connections": {"HTTP Request29": {"main": [[{"node": "Code9", "type": "main", "index": 0}]]}, "Code9": {"main": [[{"node": "Read/Write Files from Disk19", "type": "main", "index": 0}]]}, "HTTP Request30": {"main": [[{"node": "Read/Write Files from Disk20", "type": "main", "index": 0}]]}, "Read/Write Files from Disk19": {"main": [[{"node": "HTTP Request30", "type": "main", "index": 0}]]}, "AI Agent5": {"main": [[{"node": "HTTP Request29", "type": "main", "index": 0}]]}, "Google Gemini Chat Model5": {"ai_languageModel": [[{"node": "AI Agent5", "type": "ai_languageModel", "index": 0}]]}, "Google Sheets3": {"main": [[{"node": "Process One Verse at a Time2", "type": "main", "index": 0}]]}, "Process One Verse at a Time2": {"main": [[], [{"node": "AI Agent5", "type": "main", "index": 0}]]}, "Read/Write Files from Disk20": {"main": [[{"node": "HTTP Request31", "type": "main", "index": 0}]]}, "HTTP Request31": {"main": [[{"node": "Code10", "type": "main", "index": 0}]]}, "Code10": {"main": [[{"node": "Save ass2", "type": "main", "index": 0}]]}, "Save ass2": {"main": [[{"node": "AI Agent7", "type": "main", "index": 0}]]}, "Create Video1": {"main": [[]]}, "YouTube": {"main": [[{"node": "Google Sheets4", "type": "main", "index": 0}]]}, "AI Agent6": {"main": [[{"node": "Read/Write Files from Disk29", "type": "main", "index": 0}]]}, "Google Gemini Chat Model6": {"ai_languageModel": [[{"node": "AI Agent6", "type": "ai_languageModel", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "AI Agent6", "type": "ai_outputParser", "index": 0}]]}, "Read/Write Files from Disk29": {"main": [[{"node": "YouTube", "type": "main", "index": 0}]]}, "Read/Write Files from Disk30": {"main": [[{"node": "HTTP Request41", "type": "main", "index": 0}]]}, "When clicking ‘Execute workflow’": {"main": [[{"node": "Google Sheets3", "type": "main", "index": 0}]]}, "Read/Write Files from Disk21": {"main": [[{"node": "Merge2", "type": "main", "index": 0}]]}, "Read/Write Files from Disk22": {"main": [[{"node": "Merge2", "type": "main", "index": 2}]]}, "Read/Write Files from Disk23": {"main": [[{"node": "Merge2", "type": "main", "index": 3}]]}, "Read/Write Files from Disk24": {"main": [[{"node": "Merge2", "type": "main", "index": 6}]]}, "Read/Write Files from Disk25": {"main": [[{"node": "Merge2", "type": "main", "index": 7}]]}, "Read/Write Files from Disk26": {"main": [[{"node": "Merge2", "type": "main", "index": 4}]]}, "Read/Write Files from Disk27": {"main": [[{"node": "Merge2", "type": "main", "index": 5}]]}, "Read/Write Files from Disk28": {"main": [[{"node": "Merge2", "type": "main", "index": 1}]]}, "Merge2": {"main": [[{"node": "Create Video1", "type": "main", "index": 0}]]}, "HTTP Request42": {"main": [[{"node": "Convert to File4", "type": "main", "index": 0}]]}, "Convert to File4": {"main": [[{"node": "Read/Write Files from Disk21", "type": "main", "index": 0}]]}, "AI Agent7": {"main": [[{"node": "HTTP Request42", "type": "main", "index": 0}, {"node": "HTTP Request58", "type": "main", "index": 0}, {"node": "HTTP Request45", "type": "main", "index": 0}, {"node": "HTTP Request61", "type": "main", "index": 0}, {"node": "HTTP Request59", "type": "main", "index": 0}, {"node": "HTTP Request60", "type": "main", "index": 0}, {"node": "HTTP Request50", "type": "main", "index": 0}, {"node": "HTTP Request51", "type": "main", "index": 0}]]}, "Google Gemini Chat Model7": {"ai_languageModel": [[{"node": "AI Agent7", "type": "ai_languageModel", "index": 0}]]}, "HTTP Request45": {"main": [[{"node": "Convert to File5", "type": "main", "index": 0}]]}, "Convert to File5": {"main": [[{"node": "Read/Write Files from Disk22", "type": "main", "index": 0}]]}, "HTTP Request50": {"main": [[{"node": "Convert to File6", "type": "main", "index": 0}]]}, "Convert to File6": {"main": [[{"node": "Read/Write Files from Disk24", "type": "main", "index": 0}]]}, "HTTP Request51": {"main": [[{"node": "Convert to File7", "type": "main", "index": 0}]]}, "Convert to File7": {"main": [[{"node": "Read/Write Files from Disk25", "type": "main", "index": 0}]]}, "HTTP Request58": {"main": [[{"node": "Convert to File", "type": "main", "index": 0}]]}, "Convert to File": {"main": [[{"node": "Read/Write Files from Disk28", "type": "main", "index": 0}]]}, "HTTP Request59": {"main": [[{"node": "Convert to File1", "type": "main", "index": 0}]]}, "Convert to File1": {"main": [[{"node": "Read/Write Files from Disk26", "type": "main", "index": 0}]]}, "HTTP Request60": {"main": [[{"node": "Convert to File2", "type": "main", "index": 0}]]}, "Convert to File2": {"main": [[{"node": "Read/Write Files from Disk27", "type": "main", "index": 0}]]}, "HTTP Request61": {"main": [[{"node": "Convert to File3", "type": "main", "index": 0}]]}, "Convert to File3": {"main": [[{"node": "Read/Write Files from Disk23", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "0a54ce741cf01d4e2cbd981443811b275e1fabc1f5ca66c81b49d791ef299ada"}}