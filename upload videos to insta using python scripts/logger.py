import os
from datetime import datetime

def log_info(message):
    """Log an informational message to the console."""
    print(f"✅ {datetime.now().strftime('%H:%M:%S')} - {message}")

def log_error(message):
    """Log an error message to the console."""
    print(f"❌ {datetime.now().strftime('%H:%M:%S')} - {message}")

def log_warning(message):
    """Log a warning message to the console."""
    print(f"⚠️ {datetime.now().strftime('%H:%M:%S')} - {message}")

def get_uploaded_videos(log_file):
    """Get a set of uploaded video filenames from the log file."""
    if not os.path.exists(log_file):
        return set()
    with open(log_file, 'r') as f:
        return set(line.strip() for line in f)

def log_uploaded_video(log_file, video_filename):
    """Log a successfully uploaded video to the log file."""
    with open(log_file, 'a') as f:
        f.write(f"{video_filename}\n")
