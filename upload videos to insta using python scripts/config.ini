# Instagram Reels Upload Configuration
# This file contains all configuration settings for the Instagram upload script
# Edit these values as needed, then save the file

[credentials]
# Your Instagram API credentials
PAGE_TOKEN = EAAT4ejYfBZCIBPP0qlUq3UfXjKaimYGcXd7TGuMeZChuQb5tbFIFYwjVFwY2aBF2gecOXX1354dm5jafw5VKILO3pzhzasFRD4Xl3QMxZByRfey64jLtdZAs0dFlPXvu847vTwWWY03udYZCng0dKco4ZBo8y2SNPiOV0enU3MCjXgRKDnZB8YVXzNl7fV8yzdTjgTzZBdQGmQ7r
IG_BUSINESS_ID = 17841475724476384

[video_settings]
# Default video and caption settings
VIDEO_FOLDER = /Users/<USER>/development/bhagavatgeta-verse-by-verse-audio-file-for-n8n/developt-using-n8n/1stfolder/shared-data/videos
LOG_FILE = uploaded_videos.log

[google]
# Google API Key for Gemini
GOOGLE_API_KEY = AIzaSyARDGaCg7Ddw9jORagq4fA1N9WSbi4xT3I

[api_settings]
# API configuration
API_VERSION = v22.0
MAX_FILE_SIZE_MB = 100
RETRY_ATTEMPTS = 3
RETRY_DELAY = 5

[file_formats]
# Supported video formats (comma-separated)
SUPPORTED_FORMATS = .mp4,.mov,.avi
