#!/usr/bin/env python3
"""
Test script for the retry mechanism and serial upload order.
"""

import time
from main import upload_video_with_retry
from instagram import InstagramReelsUploader
from logger import log_info, log_error

def mock_uploader_with_failures():
    """Mock uploader that fails first few attempts to test retry mechanism."""
    
    class MockUploader:
        def __init__(self):
            self.attempt_count = 0
            
        def upload_reel(self, video_path, caption):
            self.attempt_count += 1
            log_info(f"Mock upload attempt {self.attempt_count}")
            
            # Simulate failure for first 2 attempts, then success
            if self.attempt_count <= 2:
                log_error(f"Mock failure on attempt {self.attempt_count}")
                return None
            else:
                log_info(f"Mock success on attempt {self.attempt_count}")
                return f"mock_media_id_{self.attempt_count}"
    
    return MockUploader()

def test_retry_mechanism():
    """Test the retry mechanism with a mock uploader."""
    
    print("🧪 Testing Retry Mechanism")
    print("=" * 50)
    
    mock_uploader = mock_uploader_with_failures()
    
    # Test successful retry after failures
    success = upload_video_with_retry(
        uploader=mock_uploader,
        video_path="test_video.mp4",
        caption="Test caption",
        filename="test_video.mp4",
        max_retries=5
    )
    
    if success:
        print("✅ Retry mechanism test PASSED")
        print(f"   - Upload succeeded after {mock_uploader.attempt_count} attempts")
    else:
        print("❌ Retry mechanism test FAILED")
    
    print("\n" + "=" * 50)

def test_serial_order_logic():
    """Test that videos would be processed in correct serial order."""
    
    print("🧪 Testing Serial Order Logic")
    print("=" * 50)
    
    # Simulate video filenames from your log
    video_names = [
        "Chapter_2_Verse_29.mp4",
        "Chapter_2_Verse_30.mp4", 
        "Chapter_2_Verse_31.mp4",
        "Chapter_1_Verse_1.mp4",    # Should be first
        "Chapter_1_Verse_10.mp4",
        "Chapter_2_Verse_1.mp4",
        "Chapter_1_Verse_2.mp4",
    ]
    
    # Sort using the same logic as main.py
    import re
    from pathlib import Path
    
    def get_chapter_verse(filename):
        """Extract chapter and verse numbers for proper sorting."""
        match = re.search(r'Chapter_(\d+)_Verse_(\d+)', filename.stem)
        if match:
            chapter, verse = map(int, match.groups())
            return (chapter, verse)
        return (999, 999)  # Put non-matching files at the end
    
    mock_files = [Path(name) for name in video_names]
    mock_files.sort(key=get_chapter_verse)
    
    print("Videos will be processed in this order:")
    for i, video_file in enumerate(mock_files, 1):
        print(f"   {i:2d}. {video_file.name}")
    
    # Check if order is correct
    expected_first = "Chapter_1_Verse_1.mp4"
    expected_second = "Chapter_1_Verse_2.mp4"
    
    if mock_files[0].name == expected_first and mock_files[1].name == expected_second:
        print("✅ Serial order test PASSED")
    else:
        print("❌ Serial order test FAILED")
    
    print("\n" + "=" * 50)

if __name__ == "__main__":
    print("🚀 Testing Enhanced Upload System with Retry Mechanism")
    print("=" * 60)
    
    test_retry_mechanism()
    test_serial_order_logic()
    
    print("🎯 Summary:")
    print("   - Videos will be processed in correct Chapter-Verse order")
    print("   - Failed uploads will retry up to 5 times with exponential backoff")
    print("   - Script stops if a video fails after all retries (maintains order)")
    print("   - Only successfully uploaded videos are logged")
    print("=" * 60)
