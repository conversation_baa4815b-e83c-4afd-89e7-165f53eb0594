#!/usr/bin/env python3
"""
Test script to verify credentials and project setup
"""

import json
import os
from google.oauth2 import service_account

def test_credentials():
    """Test if credentials file is accessible and valid."""
    
    credentials_path = "key/gen-lang-client-**********-10648e15f9ae.json"
    
    print("🔍 Testing credentials setup...")
    print("-" * 40)
    
    # Check if file exists
    if not os.path.exists(credentials_path):
        print(f"❌ Credentials file not found: {credentials_path}")
        return False
    
    print(f"✅ Credentials file found: {credentials_path}")
    
    # Load and parse JSON
    try:
        with open(credentials_path, 'r') as f:
            creds_data = json.load(f)
        
        print(f"✅ Credentials file is valid JSON")
        
        # Check required fields
        required_fields = ['type', 'project_id', 'private_key_id', 'private_key', 'client_email']
        missing_fields = [field for field in required_fields if field not in creds_data]
        
        if missing_fields:
            print(f"❌ Missing required fields: {missing_fields}")
            return False
        
        print(f"✅ All required fields present")
        print(f"📋 Project ID: {creds_data['project_id']}")
        print(f"📋 Client Email: {creds_data['client_email']}")
        print(f"📋 Service Account Type: {creds_data['type']}")
        
        # Test loading credentials with google-auth
        credentials = service_account.Credentials.from_service_account_file(
            credentials_path,
            scopes=['https://www.googleapis.com/auth/cloud-platform']
        )
        
        print(f"✅ Google Auth credentials loaded successfully")
        print(f"📋 Service Account Email: {credentials.service_account_email}")
        
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ Invalid JSON format: {e}")
        return False
    except Exception as e:
        print(f"❌ Error loading credentials: {e}")
        return False

def main():
    """Main test function."""
    
    print("🧪 Vertex AI Image Generator - Setup Test")
    print("=" * 50)
    
    if test_credentials():
        print("\n🎉 All tests passed! Ready to generate images.")
        print("\n💡 Run the main script with:")
        print("   python vertex_ai_image_generator.py")
    else:
        print("\n💥 Setup issues detected. Please fix the above errors.")
        print("\n🔧 Make sure:")
        print("   - Service account JSON file is in the correct location")
        print("   - JSON file contains all required fields")
        print("   - Service account has proper permissions")

if __name__ == "__main__":
    main()
