# Dockerignore file for N8N Bhagavad Gita Workflow

# Exclude documentation and non-essential files
*.md
README*

# Exclude development files
.git
.gitignore
*.log
*.tmp
.DS_Store

# Exclude other workflow versions (only need 8th.json)
*-idea.json
2nd-idea.json
3rdidea.json
4thidea.json
5th idea.json
6th.json
7th.json
n8n-1st-idea.json

# Exclude directories that aren't needed in the image
webscraping-for-verse/
google-sheets-app-script/

# Exclude temporary and data directories (these will be mounted as volumes)
data/
tmp/
__pycache__/
shared-data/

# Exclude build artifacts and logs
*.log
*.tmp
*.pid
*.lock

# Exclude Docker-related files from being copied into images
docker-compose*.yml
Dockerfile*
.dockerignore

# Exclude shell scripts that are not needed in containers
*.sh

# Exclude system files
Thumbs.db

# Node modules (if any exist locally)
node_modules/
npm-debug.log*

# IDE files
.vscode/
.idea/
*.swp
*.swo
